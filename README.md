# Encompass to GoHighLevel Integration

This Node.js application automatically synchronizes leads from Encompass mortgage software to GoHighLevel CRM using their respective APIs. Enhanced with web interface, lead tracking, and opportunity creation.

## Features

- **Automated Sync**: Runs continuously with configurable intervals (default: every 10 minutes)
- **Advanced Duplicate Prevention**: Sophisticated tracking system prevents duplicate processing
- **Opportunity Creation**: Automatically creates opportunities in GoHighLevel pipelines
- **Smart Data Handling**: Auto-generates placeholder emails/phones for incomplete records
- **Web Interface**: REST API endpoints for monitoring and manual triggering
- **Lead Database**: Tracks all processed leads with detailed statistics
- **Error Handling**: Comprehensive logging and error recovery with retry mechanisms
- **Configuration**: Environment-based configuration for easy deployment
- **Testing**: Built-in connection testing for both APIs

## Prerequisites

1. **Encompass API Access**:
   - Client ID and Client Secret
   - Username and Password with API access
   - Instance ID

2. **GoHighLevel API Access**:
   - API Key with contact management permissions
   - Location ID
   - Pipeline ID (optional, for opportunity creation)
   - Pipeline Stage ID (optional, for opportunity creation)

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your API credentials:
   ```env
   # Encompass API Configuration
   ENCOMPASS_API_URL=https://api.elliemae.com
   ENCOMPASS_CLIENT_ID=your_encompass_client_id
   ENCOMPASS_CLIENT_SECRET=your_encompass_client_secret
   ENCOMPASS_USERNAME=your_encompass_username
   ENCOMPASS_PASSWORD=your_encompass_password
   ENCOMPASS_INSTANCE_ID=your_encompass_instance_id

   # GoHighLevel API Configuration
   GOHIGHLEVEL_API_URL=https://services.leadconnectorhq.com
   GOHIGHLEVEL_API_KEY=your_gohighlevel_api_key
   GOHIGHLEVEL_LOCATION_ID=your_gohighlevel_location_id
   GOHIGHLEVEL_PIPELINE_ID=your_pipeline_id
   GOHIGHLEVEL_PIPELINE_STAGE_ID=your_pipeline_stage_id

   # Integration Settings
   SYNC_INTERVAL_MINUTES=10
   LOG_LEVEL=info
   PORT=3000
   ```

## Usage

### Run Continuous Sync
```bash
npm start
```
This will start the integration with web interface and run continuously, syncing leads every 10 minutes (or your configured interval).

The web interface will be available at:
- **Health Check**: `http://localhost:3000/health`
- **Status & Stats**: `http://localhost:3000/status`
- **Database Stats**: `http://localhost:3000/api/stats`
- **Manual Sync**: `POST http://localhost:3000/api/sync`

### Run One-Time Sync
```bash
node index.js --once
```
This will run a single synchronization and exit.

### Development Mode
```bash
npm run dev
```
Uses nodemon for automatic restarts during development.

### Web Interface Endpoints

- **GET /**: Service overview with basic stats
- **GET /health**: Health check endpoint
- **GET /status**: Detailed status including sync progress and recent activity
- **GET /api/stats**: Database statistics and recent lead activity
- **POST /api/sync**: Manually trigger a sync process

## How It Works

1. **Authentication**: Connects to both Encompass and GoHighLevel APIs
2. **Data Retrieval**: Fetches recent leads from Encompass
3. **Lead Database Check**: Checks internal database to prevent duplicate processing
4. **Data Transformation**: Converts Encompass loan data to GoHighLevel contact format with smart data handling
5. **Duplicate Check**: Searches for existing contacts by Encompass ID or email
6. **Contact Sync**: Creates new contacts or updates existing ones in GoHighLevel
7. **Opportunity Creation**: Creates opportunities in configured pipelines (if configured)
8. **Database Update**: Records processed leads with detailed tracking
9. **Scheduling**: Repeats the process at configured intervals

## Data Mapping

The integration maps the following fields from Encompass to GoHighLevel:

| Encompass Field | GoHighLevel Field | Description |
|----------------|-------------------|-------------|
| Field 4002 | firstName | Borrower First Name |
| Field 4000 | lastName | Borrower Last Name |
| Field 4008 | email | Borrower Email |
| Field 4009 | phone | Borrower Phone |
| Field 4010 | address1 | Property Address |
| Field 4011 | city | Property City |
| Field 4012 | state | Property State |
| Field 4013 | postalCode | Property Zip Code |
| Field 1109 | loan_amount (custom) | Loan Amount |
| Field 4003 | loan_purpose (custom) | Loan Purpose |
| Loan Number/GUID | encompass_id (custom) | Encompass Identifier |

## Enhanced Features

### Smart Data Handling
- **Auto-generated Emails**: When a lead lacks a valid email, the system generates a placeholder (e.g., `<EMAIL>`)
- **Auto-generated Phones**: When a lead lacks a valid phone, the system generates a placeholder number
- **Data Validation**: Validates email formats and phone number formats before processing
- **Tracking Flags**: Marks contacts with generated data for easy identification

### Lead Database & Tracking
- **Persistent Storage**: Maintains a JSON database of all processed leads
- **Duplicate Prevention**: Prevents reprocessing of already-handled leads
- **Statistics Tracking**: Monitors total created, updated, errors, and opportunities
- **Recent Activity**: Tracks the most recent lead processing activity
- **Data Cleanup**: Optional cleanup of old records (configurable retention period)

### Opportunity Management
- **Pipeline Integration**: Creates opportunities in specified GoHighLevel pipelines
- **Stage Assignment**: Assigns opportunities to specific pipeline stages
- **Duplicate Prevention**: Checks for existing opportunities before creating new ones
- **Monetary Value**: Sets opportunity value based on loan amount

### Web Interface & Monitoring
- **Real-time Status**: Monitor sync progress and system health
- **Manual Triggers**: Manually trigger sync processes via API
- **Statistics Dashboard**: View processing statistics and recent activity
- **Health Checks**: Endpoint for monitoring system health

## Logging

The application provides detailed logging including:
- Sync start/completion times with detailed statistics
- Number of leads processed, created, updated, skipped, and errors
- Duplicate prevention statistics
- Opportunity creation results
- Generated data tracking (emails/phones)
- Error details for troubleshooting
- API connection status
- Database operations and statistics

## Error Handling

- **API Failures**: Retries authentication and logs detailed error information
- **Data Validation**: Smart handling of incomplete data with auto-generation
- **Database Persistence**: Maintains processing state across restarts
- **Sync Conflicts**: Prevents multiple sync processes from running simultaneously
- **Graceful Shutdown**: Handles SIGINT and SIGTERM signals properly

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify your API credentials in the `.env` file
   - Check that your Encompass user has API access permissions
   - Ensure your GoHighLevel API key has contact management permissions

2. **No Leads Found**:
   - Check the date range (default is last 24 hours)
   - Verify that leads exist in Encompass with the required fields
   - Check Encompass API permissions for loan data access

3. **Contact Creation Failures**:
   - Verify GoHighLevel location ID is correct
   - Check that required fields are being mapped correctly
   - Review GoHighLevel API rate limits

### Getting API Credentials

**Encompass**:
1. Contact your Encompass administrator
2. Request API access and credentials
3. Obtain Client ID, Client Secret, and Instance ID from Encompass Developer Connect

**GoHighLevel**:
1. Log into your GoHighLevel account
2. Go to Settings > Integrations > API
3. Generate an API key with contact permissions
4. Find your Location ID in the account settings

## Support

For issues related to:
- **Encompass API**: Contact ICE Mortgage Technology support
- **GoHighLevel API**: Contact GoHighLevel support
- **This Integration**: Check the logs for detailed error information

## License

MIT License - feel free to modify and distribute as needed.

require('dotenv').config();
const axios = require('axios');

// Configuration from environment variables
const API_SERVER = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;

console.log('🚀 Starting Encompass Data Fetch Script');
console.log('='.repeat(60));
console.log(`📡 API Server: ${API_SERVER}`);
console.log(`👤 Username: ${username}`);
console.log('='.repeat(60));

// Authentication function
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${API_SERVER}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

// Function to fetch data from endpoint
async function fetchData(url, token, description) {
    try {
        console.log(`\n📥 Fetching data from: ${description}`);
        console.log(`🔗 URL: ${url}`);
        
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = response.data;
        const count = Array.isArray(data) ? data.length : (data.length || 'unknown');
        
        console.log(`✅ Successfully fetched data`);
        console.log(`📊 Data count: ${count}`);
        console.log(`📋 Sample data structure:`, JSON.stringify(data.slice ? data.slice(0, 2) : data, null, 2));
        
        return {
            url,
            description,
            count,
            data,
            fetchedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error(`❌ Error fetching ${description}:`, error.response?.data || error.message);
        return {
            url,
            description,
            count: 0,
            data: null,
            error: error.response?.data || error.message,
            fetchedAt: new Date().toISOString()
        };
    }
}

// Main function
async function main() {
    try {
        // Get access token
        const token = await getAccessToken();
        
        // Define the endpoints to fetch
        const endpoints = [
            {
                url: `${API_SERVER}/encompass/v1/borrowerContactSelector?start=0&limit=10000`,
                description: 'Borrower Contact Selector (start=0, limit=10000)'
            },
            {
                url: `${API_SERVER}/encompass/v1/borrowerContactSelector?start=1&limit=10000&cursorType=RandomAccess`,
                description: 'Borrower Contact Selector (start=1, limit=10000, RandomAccess)'
            }
        ];

        console.log('\n🎯 Starting data fetch from both endpoints...');
        console.log('='.repeat(60));

        const results = [];
        let totalCount = 0;

        // Fetch data from each endpoint
        for (const endpoint of endpoints) {
            const result = await fetchData(endpoint.url, token, endpoint.description);
            results.push(result);
            
            if (result.count && typeof result.count === 'number') {
                totalCount += result.count;
            }
            
            // Add delay between requests
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Summary
        console.log('\n📈 FETCH SUMMARY');
        console.log('='.repeat(60));
        console.log(`🔢 Total endpoints fetched: ${endpoints.length}`);
        console.log(`📊 Total records across all endpoints: ${totalCount}`);
        
        results.forEach((result, index) => {
            console.log(`\n📋 Endpoint ${index + 1}: ${result.description}`);
            console.log(`   📊 Count: ${result.count}`);
            console.log(`   ⏰ Fetched at: ${result.fetchedAt}`);
            if (result.error) {
                console.log(`   ❌ Error: ${result.error}`);
            }
        });

        // Log detailed data for each endpoint
        console.log('\n📄 DETAILED DATA LOGS');
        console.log('='.repeat(60));
        
        results.forEach((result, index) => {
            console.log(`\n🔍 ENDPOINT ${index + 1} DATA: ${result.description}`);
            console.log('-'.repeat(50));
            
            if (result.data) {
                if (Array.isArray(result.data)) {
                    console.log(`📊 Array with ${result.data.length} items`);
                    if (result.data.length > 0) {
                        console.log('📋 First few items:');
                        result.data.slice(0, 5).forEach((item, i) => {
                            console.log(`   ${i + 1}. ${JSON.stringify(item)}`);
                        });
                        
                        if (result.data.length > 5) {
                            console.log(`   ... and ${result.data.length - 5} more items`);
                        }
                    }
                } else {
                    console.log('📋 Full data:');
                    console.log(JSON.stringify(result.data, null, 2));
                }
            } else {
                console.log('❌ No data received');
            }
        });

        console.log('\n✅ Fetch script completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();

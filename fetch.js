require('dotenv').config();
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration from environment variables
const API_SERVER = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;

console.log('🚀 Starting Encompass Data Fetch Script');
console.log('='.repeat(60));
console.log(`📡 API Server: ${API_SERVER}`);
console.log(`👤 Username: ${username}`);
console.log('='.repeat(60));

// Authentication function
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${API_SERVER}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

// Function to fetch data from endpoint with POST body
async function fetchData(url, token, description, body) {
    try {
        console.log(`\n📥 Fetching data from: ${description}`);
        console.log(`🔗 URL: ${url}`);
        console.log(`📋 Request body:`, JSON.stringify(body, null, 2));

        const response = await axios.post(url, body, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = response.data;
        const count = Array.isArray(data) ? data.length : (data.length || 'unknown');

        console.log(`✅ Successfully fetched data`);
        console.log(`📊 Data count: ${count}`);
        console.log(`📋 Sample data structure:`, JSON.stringify(data.slice ? data.slice(0, 2) : data, null, 2));

        return {
            url,
            description,
            count,
            data,
            body,
            fetchedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error(`❌ Error fetching ${description}:`, error.response?.data || error.message);
        return {
            url,
            description,
            count: 0,
            data: null,
            body,
            error: error.response?.data || error.message,
            fetchedAt: new Date().toISOString()
        };
    }
}

// Get detailed borrower contact information
async function getBorrowerContactDetails(contactId, token) {
    try {
        console.log(`   📋 Fetching details for contact: ${contactId}`);

        const response = await axios.get(`${API_SERVER}/encompass/v1/borrowerContacts/${contactId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data;
    } catch (error) {
        console.error(`   ❌ Error fetching contact details for ${contactId}:`, error.response?.data || error.message);
        return null;
    }
}

// Extract comprehensive data from borrower contact (using direct field names from API)
function extractComprehensiveData(borrowerContact, loanData = null, loanAssociates = null, realtorData = null) {
    const comprehensiveRecord = {
        // Borrower Information (from borrower contacts API)
        borrower: {
            id: borrowerContact.id,
            name: `${borrowerContact.firstName || ''} ${borrowerContact.lastName || ''}`.trim(),
            firstName: borrowerContact.firstName,
            lastName: borrowerContact.lastName,
            email: borrowerContact.personalEmail || borrowerContact.businessEmail,
            phones: {
                home: borrowerContact.homePhone,
                work: borrowerContact.workPhone,
                mobile: borrowerContact.mobilePhone,
                primary: borrowerContact.primaryPhone
            },
            dateOfBirth: borrowerContact.birthdate,
            address: borrowerContact.currentMailingAddress,
            businessAddress: borrowerContact.bizAddress,
            employer: {
                name: borrowerContact.employerName,
                jobTitle: borrowerContact.jobTitle
            },
            borrowerType: borrowerContact.borrowerType,
            salutation: borrowerContact.salutation,
            faxNumber: borrowerContact.faxNumber,
            businessWebUrl: borrowerContact.businessWebUrl,
            ownerId: borrowerContact.ownerId,
            accessLevel: borrowerContact.accessLevel
        },

        // Loan Information (from loan APIs when available)
        loan: {
            id: loanData?.loanNumber || null,
            guid: loanData?.guid || null,
            interestRate: null,
            closingDate: null,
            propertyAddress: null,
            loanOriginator: null,
            amount: null,
            type: null
        },

        // Enhanced Realtor Information
        realtor: {
            name: borrowerContact.referral,
            phone: realtorData?.phone || realtorData?.workPhone || realtorData?.mobilePhone || null,
            email: realtorData?.email || realtorData?.businessEmail || realtorData?.personalEmail || null,
            businessContactId: realtorData?.id || null,
            businessName: realtorData?.name || null,
            category: realtorData?.category || null,
            address: realtorData?.address || null,
            website: realtorData?.businessWebUrl || null
        },

        // Data source tracking
        dataSources: {
            borrowerContact: !!borrowerContact,
            loanData: !!loanData,
            loanAssociates: !!loanAssociates,
            realtorBusinessContact: !!realtorData
        },

        // Metadata
        metadata: {
            extractedAt: new Date().toISOString(),
            source: 'Encompass API - Enhanced',
            version: '2.0',
            hasRealtorData: !!realtorData,
            hasLoanData: !!loanData
        }
    };

    return comprehensiveRecord;
}

// Main function
async function main() {
    try {
        // Get access token
        const token = await getAccessToken();
        
        // Dynamic pagination - fetch up to 10 pages automatically
        const MAX_PAGES = 10;
        const PAGE_SIZE = 1000;

        console.log('\n🎯 Starting dynamic pagination fetch...');
        console.log('='.repeat(60));

        const results = [];
        let totalCount = 0;
        let currentPage = 0;
        let hasMoreData = true;

        // Dynamic pagination loop
        while (hasMoreData && currentPage < MAX_PAGES) {
            const start = currentPage * PAGE_SIZE;
            const pageNum = currentPage + 1;

            const endpoint = {
                url: `${API_SERVER}/encompass/v1/borrowerContactSelector`,
                description: `Borrower Contact Selector - Page ${pageNum} (start=${start}, limit=${PAGE_SIZE})`,
                body: {
                    "start": start,
                    "limit": PAGE_SIZE
                }
            };

            console.log(`\n📄 Fetching Page ${pageNum}/${MAX_PAGES}...`);
            const result = await fetchData(endpoint.url, token, endpoint.description, endpoint.body);
            results.push(result);

            if (result.count && typeof result.count === 'number') {
                totalCount += result.count;

                // Check if we got less than the page size - means no more data
                if (result.count < PAGE_SIZE) {
                    hasMoreData = false;
                    console.log(`✅ Reached end of data at page ${pageNum} (got ${result.count} records)`);
                }
            } else {
                // Error or no data - stop pagination
                hasMoreData = false;
                if (result.error) {
                    console.log(`❌ Error on page ${pageNum} - stopping pagination`);
                }
            }

            currentPage++;

            // Add delay between requests
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        if (currentPage >= MAX_PAGES && hasMoreData) {
            console.log(`\n⚠️ Reached maximum pages (${MAX_PAGES}) - there might be more data available!`);
        }

        // Summary
        console.log('\n📈 FETCH SUMMARY');
        console.log('='.repeat(60));
        console.log(`🔢 Total pages fetched: ${results.length}`);
        console.log(`📊 Total records across all pages: ${totalCount}`);
        console.log(`🎯 Dynamic pagination: ${hasMoreData ? 'Stopped due to page limit' : 'Reached end of data'}`);

        results.forEach((result, index) => {
            console.log(`\n📋 Page ${index + 1}: ${result.description}`);
            console.log(`   📊 Count: ${result.count}`);
            console.log(`   ⏰ Fetched at: ${result.fetchedAt}`);
            if (result.error) {
                console.log(`   ❌ Error: ${result.error}`);
            } else if (result.count === 0) {
                console.log(`   ⚠️ No data returned`);
            }
        });

        // Process and extract comprehensive data by fetching full contact details
        console.log('\n🔄 PROCESSING COMPREHENSIVE DATA');
        console.log('='.repeat(60));

        const allComprehensiveData = [];
        let processedCount = 0;
        const BATCH_SIZE = 50; // Process in smaller batches to avoid overwhelming the API

        for (let pageIndex = 0; pageIndex < results.length; pageIndex++) {
            const result = results[pageIndex];
            console.log(`\n🔍 PROCESSING PAGE ${pageIndex + 1}: ${result.description}`);
            console.log('-'.repeat(50));

            if (result.data && Array.isArray(result.data)) {
                console.log(`📊 Fetching detailed data for ${result.data.length} borrower contact IDs...`);

                for (let i = 0; i < result.data.length; i++) {
                    const contactId = result.data[i].id;

                    if (!contactId) {
                        console.log(`   ⚠️ Skipping contact ${i + 1} - no ID found`);
                        continue;
                    }

                    try {
                        // Fetch detailed contact information
                        const detailedContact = await getBorrowerContactDetails(contactId, token);

                        if (detailedContact) {
                            const comprehensiveData = extractComprehensiveData(detailedContact);
                            allComprehensiveData.push(comprehensiveData);
                            processedCount++;

                            // Log progress every 50 contacts
                            if (processedCount % BATCH_SIZE === 0) {
                                console.log(`   📊 Processed ${processedCount} contacts so far...`);
                            }

                            // Log first few processed records from first page
                            if (pageIndex === 0 && i < 3) {
                                console.log(`\n📋 Sample Contact ${i + 1}:`);
                                console.log(`   👤 Name: ${comprehensiveData.borrower.name}`);
                                console.log(`   📧 Email: ${comprehensiveData.borrower.email}`);
                                console.log(`   📱 Phone: ${comprehensiveData.borrower.phones.mobile || comprehensiveData.borrower.phones.home || comprehensiveData.borrower.phones.work}`);
                                console.log(`   🏠 Address: ${comprehensiveData.borrower.address ? JSON.stringify(comprehensiveData.borrower.address) : 'N/A'}`);
                                console.log(`   💼 Employer: ${comprehensiveData.borrower.employer.name || 'N/A'}`);
                                console.log(`   🤝 Referral: ${comprehensiveData.realtor.name || 'N/A'}`);
                            }
                        } else {
                            console.log(`   ❌ Failed to get details for contact ${contactId}`);
                        }

                        // Rate limiting - small delay between detail requests
                        await new Promise(resolve => setTimeout(resolve, 100));

                    } catch (error) {
                        console.error(`   ❌ Error processing contact ${contactId}:`, error.message);
                    }
                }

                console.log(`   ✅ Completed page ${pageIndex + 1} - processed ${result.data.length} contact IDs`);
            } else {
                console.log('❌ No contact IDs to process');
            }
        }

        console.log(`\n🎉 PROCESSING COMPLETE: ${processedCount} detailed contacts processed from ${totalCount} contact IDs`);

        // Log detailed data for each endpoint (raw data)
        console.log('\n📄 RAW DATA SAMPLES');
        console.log('='.repeat(60));

        results.forEach((result, index) => {
            console.log(`\n🔍 ENDPOINT ${index + 1} RAW DATA: ${result.description}`);
            console.log('-'.repeat(50));

            if (result.data && Array.isArray(result.data) && result.data.length > 0) {
                console.log(`📊 Sample of first 2 raw records:`);
                result.data.slice(0, 2).forEach((item, i) => {
                    console.log(`\n   Record ${i + 1}:`);
                    console.log(`   ${JSON.stringify(item, null, 4)}`);
                });
            } else {
                console.log('❌ No raw data available');
            }
        });

        // Final comprehensive data summary
        console.log('\n📈 COMPREHENSIVE DATA SUMMARY');
        console.log('='.repeat(60));
        console.log(`🔢 Total comprehensive records created: ${allComprehensiveData.length}`);
        console.log(`📄 Pages processed: ${results.filter(r => !r.error).length}/${results.length}`);

        // Show records per page breakdown
        console.log('\n📊 Records per page:');
        results.forEach((result, index) => {
            if (!result.error) {
                console.log(`   Page ${index + 1}: ${result.count} records`);
            } else {
                console.log(`   Page ${index + 1}: ERROR - ${result.error}`);
            }
        });

        if (allComprehensiveData.length > 0) {
            const withEmails = allComprehensiveData.filter(record => record.borrower.email).length;
            const withPhones = allComprehensiveData.filter(record =>
                record.borrower.phones.mobile || record.borrower.phones.home || record.borrower.phones.work
            ).length;
            const withAddresses = allComprehensiveData.filter(record => record.borrower.address).length;
            const withEmployers = allComprehensiveData.filter(record => record.borrower.employer.name).length;
            const withReferrals = allComprehensiveData.filter(record => record.realtor.name).length;

            console.log('\n📈 Data Quality Metrics:');
            console.log(`📧 Records with emails: ${withEmails} (${((withEmails/allComprehensiveData.length)*100).toFixed(1)}%)`);
            console.log(`📱 Records with phones: ${withPhones} (${((withPhones/allComprehensiveData.length)*100).toFixed(1)}%)`);
            console.log(`🏠 Records with addresses: ${withAddresses} (${((withAddresses/allComprehensiveData.length)*100).toFixed(1)}%)`);
            console.log(`💼 Records with employers: ${withEmployers} (${((withEmployers/allComprehensiveData.length)*100).toFixed(1)}%)`);
            console.log(`🤝 Records with referrals: ${withReferrals} (${((withReferrals/allComprehensiveData.length)*100).toFixed(1)}%)`);

            console.log('\n📋 Sample comprehensive record:');
            console.log(JSON.stringify(allComprehensiveData[0], null, 2));
        }

        // Save all data to JSON files
        console.log('\n💾 SAVING DATA TO JSON FILES');
        console.log('='.repeat(60));

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

        try {
            // Save raw data from all pages
            const rawDataFile = `encompass-raw-data-${timestamp}.json`;
            const rawDataToSave = {
                metadata: {
                    fetchedAt: new Date().toISOString(),
                    totalPages: results.length,
                    totalRecords: totalCount,
                    paginationComplete: !hasMoreData,
                    apiServer: API_SERVER
                },
                pages: results.map((result, index) => ({
                    pageNumber: index + 1,
                    description: result.description,
                    count: result.count,
                    fetchedAt: result.fetchedAt,
                    error: result.error || null,
                    data: result.data || []
                }))
            };

            fs.writeFileSync(rawDataFile, JSON.stringify(rawDataToSave, null, 2));
            console.log(`✅ Raw data saved to: ${rawDataFile}`);
            console.log(`   📊 File size: ${(fs.statSync(rawDataFile).size / 1024 / 1024).toFixed(2)} MB`);

            // Save comprehensive processed data
            if (allComprehensiveData.length > 0) {
                const comprehensiveDataFile = `encompass-comprehensive-data-${timestamp}.json`;
                const comprehensiveDataToSave = {
                    metadata: {
                        processedAt: new Date().toISOString(),
                        totalRecords: allComprehensiveData.length,
                        source: 'Encompass API - Dynamic Pagination',
                        version: '2.0'
                    },
                    comprehensiveData: allComprehensiveData
                };

                fs.writeFileSync(comprehensiveDataFile, JSON.stringify(comprehensiveDataToSave, null, 2));
                console.log(`✅ Comprehensive data saved to: ${comprehensiveDataFile}`);
                console.log(`   📊 File size: ${(fs.statSync(comprehensiveDataFile).size / 1024 / 1024).toFixed(2)} MB`);
            }

            // Save summary report
            const summaryFile = `encompass-fetch-summary-${timestamp}.json`;
            const summaryData = {
                fetchSummary: {
                    timestamp: new Date().toISOString(),
                    totalPages: results.length,
                    totalRecords: totalCount,
                    paginationComplete: !hasMoreData,
                    successfulPages: results.filter(r => !r.error).length,
                    failedPages: results.filter(r => r.error).length
                },
                pageBreakdown: results.map((result, index) => ({
                    page: index + 1,
                    records: result.count,
                    success: !result.error,
                    error: result.error || null
                })),
                dataQuality: allComprehensiveData.length > 0 ? {
                    totalRecords: allComprehensiveData.length,
                    withEmails: allComprehensiveData.filter(record => record.borrower.email).length,
                    withPhones: allComprehensiveData.filter(record =>
                        record.borrower.phones.mobile || record.borrower.phones.home || record.borrower.phones.work
                    ).length,
                    withAddresses: allComprehensiveData.filter(record => record.borrower.address).length,
                    withEmployers: allComprehensiveData.filter(record => record.borrower.employer.name).length,
                    withReferrals: allComprehensiveData.filter(record => record.realtor.name).length
                } : null
            };

            fs.writeFileSync(summaryFile, JSON.stringify(summaryData, null, 2));
            console.log(`✅ Summary report saved to: ${summaryFile}`);

        } catch (error) {
            console.error('❌ Error saving JSON files:', error.message);
        }

        console.log('\n✅ Fetch script completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();

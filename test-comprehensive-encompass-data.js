const axios = require('axios');
const fs = require('fs');
const path = require('path');
const cron = require('node-cron');
require('dotenv').config();

console.log('🧪 Encompass Comprehensive Data Fetcher - All Required Fields');
console.log('='.repeat(70));
console.log('📋 Usage:');
console.log('   node test-comprehensive-encompass-data.js [limit] [mode]');
console.log('   - limit: Number of records to fetch (default: 1000)');
console.log('   - mode: "sync", "cron", "contacts", or "comprehensive" (default: sync)');
console.log('');
console.log('📋 Examples:');
console.log('   node test-comprehensive-encompass-data.js 0 sync         # Incremental sync to GoHighLevel');
console.log('   node test-comprehensive-encompass-data.js 0 cron         # Start cron job (every 6 hours)');
console.log('   node test-comprehensive-encompass-data.js 1000 contacts  # Fetch 1000 contacts only');
console.log('   node test-comprehensive-encompass-data.js 25 comprehensive # Fetch 25 comprehensive records');
console.log('');
console.log('📋 Modes:');
console.log('   🔄 sync: Incremental sync to GoHighLevel with COMPREHENSIVE DATA');
console.log('   ⏰ cron: Start scheduled sync every 6 hours + initial sync');
console.log('   📥 contacts: Fetch contacts from Encompass (save to JSON)');
console.log('   🎯 comprehensive: Fetch comprehensive data with loan details');
console.log('');
console.log('📋 Comprehensive Data Synced to GoHighLevel:');
console.log('   ✅ Borrower Contacts: name, email, phone, DOB, address, employer');
console.log('   ✅ Business Contacts: name, email, phone, category');
console.log('   🎯 LOAN DATA: Interest rates, closing dates, loan amounts, loan types');
console.log('   👨‍💼 LOAN ORIGINATORS: Names, emails, phones, roles');
console.log('   🏠 REALTOR INFO: Names, contact details from referrals');
console.log('   📊 PROPERTY DATA: Property addresses, loan details');
console.log('   🔄 GoHighLevel Sync: Automatic incremental sync with comprehensive tracking');
console.log('='.repeat(70));

// Configuration
const baseUrl = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;

// GoHighLevel Configuration
const GHL_CONFIG = {
    apiKey: process.env.GOHIGHLEVEL_API_KEY,
    baseUrl: process.env.GOHIGHLEVEL_API_URL || 'https://services.leadconnectorhq.com',
    locationId: process.env.GOHIGHLEVEL_LOCATION_ID
};

// Sync Configuration
const SYNC_CONFIG = {
    batchSize: 100,
    delayBetweenRequests: 200,
    maxRetries: 3,
    cronSchedule: '0 */6 * * *' // Every 6 hours
};

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Database file paths for incremental sync
const SYNC_DB_PATH = path.join(__dirname, 'sync-database.json');
const SYNC_LOGS_PATH = path.join(__dirname, 'sync-logs.json');

// Authentication function
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${baseUrl}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

// ==================== DATABASE MANAGEMENT ====================

function loadSyncDatabase() {
    try {
        if (fs.existsSync(SYNC_DB_PATH)) {
            const data = JSON.parse(fs.readFileSync(SYNC_DB_PATH, 'utf8'));
            console.log(`📊 Loaded sync database: ${data.totalProcessed || 0} contacts processed`);
            return data;
        }
    } catch (error) {
        console.error('❌ Error loading sync database:', error.message);
    }

    return {
        totalProcessed: 0,
        lastSyncTime: null,
        borrowerCount: 0,
        businessCount: 0,
        totalContacts: 0,
        processedContacts: [],
        failedContacts: [],
        syncHistory: []
    };
}

function saveSyncDatabase(database) {
    try {
        fs.writeFileSync(SYNC_DB_PATH, JSON.stringify(database, null, 2));
        console.log(`💾 Sync database saved: ${database.totalProcessed} contacts processed`);
    } catch (error) {
        console.error('❌ Error saving sync database:', error.message);
    }
}

function loadSyncLogs() {
    try {
        if (fs.existsSync(SYNC_LOGS_PATH)) {
            return JSON.parse(fs.readFileSync(SYNC_LOGS_PATH, 'utf8'));
        }
    } catch (error) {
        console.error('❌ Error loading sync logs:', error.message);
    }
    return { syncRuns: [] };
}

function saveSyncLogs(logs) {
    try {
        fs.writeFileSync(SYNC_LOGS_PATH, JSON.stringify(logs, null, 2));
    } catch (error) {
        console.error('❌ Error saving sync logs:', error.message);
    }
}

// ==================== GOHIGHLEVEL API ====================

async function createGHLContact(contactData) {
    try {
        const response = await axios.post(
            `${GHL_CONFIG.baseUrl}/contacts/`,
            contactData,
            {
                headers: {
                    'Authorization': `Bearer ${GHL_CONFIG.apiKey}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log(`✅ Created GHL contact: ${contactData.firstName} ${contactData.lastName}`);
        return response.data;
    } catch (error) {
        if (error.response?.status === 422 && error.response?.data?.message?.includes('already exists')) {
            console.log(`⚠️ Contact already exists in GHL: ${contactData.firstName} ${contactData.lastName}`);
            return { status: 'exists', contact: contactData };
        }

        console.error(`❌ Error creating GHL contact:`, error.response?.data || error.message);
        throw error;
    }
}

async function updateGHLContact(contactId, contactData) {
    try {
        const response = await axios.put(
            `${GHL_CONFIG.baseUrl}/contacts/${contactId}`,
            contactData,
            {
                headers: {
                    'Authorization': `Bearer ${GHL_CONFIG.apiKey}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log(`✅ Updated GHL contact: ${contactData.firstName} ${contactData.lastName}`);
        return response.data;
    } catch (error) {
        console.error(`❌ Error updating GHL contact:`, error.response?.data || error.message);
        throw error;
    }
}

async function searchGHLContact(email, phone) {
    try {
        let searchUrl = `${GHL_CONFIG.baseUrl}/contacts/search?locationId=${GHL_CONFIG.locationId}`;

        if (email) {
            searchUrl += `&email=${encodeURIComponent(email)}`;
        } else if (phone) {
            searchUrl += `&phone=${encodeURIComponent(phone)}`;
        }

        const response = await axios.get(searchUrl, {
            headers: {
                'Authorization': `Bearer ${GHL_CONFIG.apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        return response.data.contacts || [];
    } catch (error) {
        console.error(`❌ Error searching GHL contact:`, error.response?.data || error.message);
        return [];
    }
}

// ==================== DATA TRANSFORMATION ====================

function transformToGHLFormat(encompassContact, type = 'borrower') {
    const phones = [];
    if (encompassContact.homePhone) phones.push(encompassContact.homePhone);
    if (encompassContact.workPhone) phones.push(encompassContact.workPhone);
    if (encompassContact.mobilePhone) phones.push(encompassContact.mobilePhone);
    if (encompassContact.phone) phones.push(encompassContact.phone);

    const primaryPhone = phones[0] || '';
    const address = encompassContact.currentMailingAddress || encompassContact.address;
    const fullAddress = address ?
        `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim() : '';

    const ghlContact = {
        // Standard GoHighLevel fields
        firstName: encompassContact.firstName || encompassContact.contactName || '',
        lastName: encompassContact.lastName || '',
        name: `${encompassContact.firstName || encompassContact.contactName || ''} ${encompassContact.lastName || ''}`.trim(),
        email: encompassContact.personalEmail || encompassContact.businessEmail || encompassContact.email || '',
        phone: primaryPhone,
        address1: address?.street1 || '',
        city: address?.city || '',
        state: address?.state || '',
        postalCode: address?.zip || '',
        country: 'US',
        source: 'Encompass API',
        locationId: GHL_CONFIG.locationId,

        // Custom fields for comprehensive borrower data
        customFields: [
            { key: 'encompass_contact_id', field_value: encompassContact.id || '' },
            { key: 'contact_type', field_value: type },
            { key: 'import_date', field_value: new Date().toISOString() },
            { key: 'sync_status', field_value: 'synced' },
            { key: 'full_address', field_value: fullAddress }
        ],

        // Tags for organization and tracking
        tags: ['Encompass Import', type === 'borrower' ? 'Borrower' : 'Business Contact']
    };

    // Add comprehensive borrower-specific fields
    if (type === 'borrower') {
        ghlContact.customFields.push(
            { key: 'date_of_birth', field_value: encompassContact.birthdate || '' },
            { key: 'home_phone', field_value: encompassContact.homePhone || '' },
            { key: 'work_phone', field_value: encompassContact.workPhone || '' },
            { key: 'mobile_phone', field_value: encompassContact.mobilePhone || '' },
            { key: 'business_email', field_value: encompassContact.businessEmail || '' },
            { key: 'personal_email', field_value: encompassContact.personalEmail || '' },
            { key: 'realtor_name', field_value: encompassContact.referral || '' },
            { key: 'borrower_type', field_value: 'Primary Borrower' },
            { key: 'employer_name', field_value: encompassContact.employerName || '' },
            { key: 'job_title', field_value: encompassContact.jobTitle || '' },

            // Placeholder fields for loan data (when comprehensive data is available)
            { key: 'interest_rate', field_value: '' },
            { key: 'closing_date', field_value: '' },
            { key: 'loan_originator', field_value: '' },
            { key: 'realtor_phone', field_value: '' },
            { key: 'realtor_email', field_value: '' },
            { key: 'property_address', field_value: '' },
            { key: 'loan_amount', field_value: '' },
            { key: 'loan_type', field_value: '' }
        );
        ghlContact.tags.push('Mortgage Lead', 'Real Estate');
    } else {
        ghlContact.customFields.push(
            { key: 'business_name', field_value: encompassContact.name || '' },
            { key: 'contact_name', field_value: encompassContact.contactName || '' },
            { key: 'business_category', field_value: encompassContact.category || '' }
        );
        ghlContact.tags.push('Business Partner', 'Professional Services');
    }

    // Remove empty custom fields
    ghlContact.customFields = ghlContact.customFields.filter(field => field.field_value !== '');

    return ghlContact;
}

// Transform comprehensive data (with loan info) to GoHighLevel format
function transformComprehensiveToGHL(comprehensiveRecord) {
    try {
        const borrower = comprehensiveRecord?.borrower || {};
        const loan = comprehensiveRecord?.loan || {};
        const realtor = comprehensiveRecord?.realtor || {};

        // Handle phone numbers safely
        const phones = [];
        if (borrower.phones?.home) phones.push(borrower.phones.home);
        if (borrower.phones?.work) phones.push(borrower.phones.work);
        if (borrower.phones?.mobile) phones.push(borrower.phones.mobile);

        // Fallback to direct phone fields if phones object doesn't exist
        if (phones.length === 0) {
            if (borrower.homePhone) phones.push(borrower.homePhone);
            if (borrower.workPhone) phones.push(borrower.workPhone);
            if (borrower.mobilePhone) phones.push(borrower.mobilePhone);
        }

        const primaryPhone = phones[0] || '';
        const address = borrower.address || borrower.currentMailingAddress || {};
        const fullAddress = address ?
            `${address.street1 || ''} ${address.street2 || ''}, ${address.city || ''}, ${address.state || ''} ${address.zip || ''}`.trim() : '';

        return {
            // Standard GoHighLevel fields
            firstName: borrower.firstName || '',
            lastName: borrower.lastName || '',
            name: borrower.name || `${borrower.firstName || ''} ${borrower.lastName || ''}`.trim(),
            email: borrower.email || borrower.personalEmail || borrower.businessEmail || '',
            phone: primaryPhone,
            address1: address?.street1 || '',
            city: address?.city || '',
            state: address?.state || '',
            postalCode: address?.zip || '',
            country: 'US',
            source: 'Encompass API - Comprehensive',
            locationId: GHL_CONFIG.locationId,

            // Comprehensive custom fields with loan data
            customFields: [
                { key: 'encompass_contact_id', field_value: borrower.id || '' },
                { key: 'contact_type', field_value: 'borrower' },
                { key: 'import_date', field_value: new Date().toISOString() },
                { key: 'sync_status', field_value: 'comprehensive_synced' },
                { key: 'date_of_birth', field_value: borrower.dateOfBirth || borrower.birthdate || '' },
                { key: 'home_phone', field_value: borrower.phones?.home || borrower.homePhone || '' },
                { key: 'work_phone', field_value: borrower.phones?.work || borrower.workPhone || '' },
                { key: 'mobile_phone', field_value: borrower.phones?.mobile || borrower.mobilePhone || '' },
                { key: 'full_address', field_value: fullAddress },
                { key: 'borrower_type', field_value: 'Primary Borrower' },
                { key: 'employer_name', field_value: borrower.employer?.name || borrower.employerName || '' },
                { key: 'job_title', field_value: borrower.employer?.jobTitle || borrower.jobTitle || '' },

                // Loan information
                { key: 'loan_id', field_value: loan?.id || '' },
                { key: 'loan_guid', field_value: loan?.guid || '' },
                { key: 'interest_rate', field_value: loan?.interestRate || '' },
                { key: 'closing_date', field_value: loan?.closingDate || '' },
                { key: 'property_address', field_value: loan?.propertyAddress || '' },
                { key: 'loan_amount', field_value: loan?.amount || '' },
                { key: 'loan_type', field_value: loan?.type || '' },

                // Loan originator information
                { key: 'loan_originator', field_value: loan?.loanOriginator?.name || '' },
                { key: 'loan_originator_email', field_value: loan?.loanOriginator?.email || '' },
                { key: 'loan_originator_phone', field_value: loan?.loanOriginator?.phone || '' },
                { key: 'loan_originator_role', field_value: loan?.loanOriginator?.role || '' },

                // Enhanced Realtor information
                { key: 'realtor_name', field_value: realtor?.name || borrower.referral || '' },
                { key: 'realtor_phone', field_value: realtor?.phone || '' },
                { key: 'realtor_email', field_value: realtor?.email || '' },
                { key: 'realtor_business_id', field_value: realtor?.businessContactId || '' },
                { key: 'realtor_business_name', field_value: realtor?.businessName || '' },
                { key: 'realtor_category', field_value: realtor?.category || '' },
                { key: 'realtor_website', field_value: realtor?.website || '' },

                // Enhanced borrower information
                { key: 'borrower_type_code', field_value: borrower.borrowerType || '' },
                { key: 'salutation', field_value: borrower.salutation || '' },
                { key: 'fax_number', field_value: borrower.faxNumber || '' },
                { key: 'business_web_url', field_value: borrower.businessWebUrl || '' },
                { key: 'owner_id', field_value: borrower.ownerId || '' },
                { key: 'access_level', field_value: borrower.accessLevel || '' },
                { key: 'primary_phone', field_value: borrower.phones?.primary || '' },

                // Business address if different from mailing
                { key: 'business_address', field_value: borrower.businessAddress ?
                    `${borrower.businessAddress.street1 || ''} ${borrower.businessAddress.street2 || ''}, ${borrower.businessAddress.city || ''}, ${borrower.businessAddress.state || ''} ${borrower.businessAddress.zip || ''}`.trim() : '' },

                // Data source tracking
                { key: 'data_sources', field_value: JSON.stringify(comprehensiveRecord.dataSources || {}) },
                { key: 'extracted_at', field_value: comprehensiveRecord.metadata?.extractedAt || '' },
                { key: 'data_version', field_value: comprehensiveRecord.metadata?.version || '' }
            ].filter(field => field.field_value !== ''), // Remove empty custom fields

            // Enhanced tags for comprehensive data
            tags: [
                'Encompass Import',
                'Comprehensive Data',
                'Borrower',
                'Mortgage Lead',
                'Real Estate',
                loan?.id ? 'Has Loan Data' : 'Contact Only',
                realtor?.name || borrower.referral ? 'Has Realtor' : 'No Realtor'
            ]
        };
    } catch (error) {
        console.error('❌ Error in transformComprehensiveToGHL:', error.message);
        // Fallback to basic transformation if comprehensive fails
        return transformToGHLFormat(comprehensiveRecord, 'borrower');
    }
}

class ComprehensiveDataFetcher {
    constructor() {
        this.fetchedData = [];
        this.startTime = Date.now();
        this.apiCallCount = 0;
        this.syncDatabase = loadSyncDatabase();
        this.syncLogs = loadSyncLogs();
    }

    // API call wrapper with rate limiting and counting
    async makeApiCall(url, options, description) {
        this.apiCallCount++;
        console.log(`🌐 API Call ${this.apiCallCount}: ${description}`);
        
        try {
            const response = await axios(url, options);
            await new Promise(resolve => setTimeout(resolve, 200)); // Rate limiting
            return response.data;
        } catch (error) {
            console.error(`❌ ${description} failed:`, error.response?.data || error.message);
            return null;
        }
    }

    // Get total count of borrower contacts available
    async getBorrowerContactsCount(token) {
        console.log(`📊 Getting total count of borrower contacts...`);

        const data = await this.makeApiCall(
            `${baseUrl}/encompass/v1/borrowerContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 1, limit: 1 } // Just get 1 to see total available
            },
            `Getting total borrower contacts count`
        );

        if (data && Array.isArray(data)) {
            // Try to get a larger sample to estimate total
            const largeSample = await this.makeApiCall(
                `${baseUrl}/encompass/v1/borrowerContactSelector`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { start: 1, limit: 10000 } // Try to get max possible
                },
                `Getting maximum borrower contacts to determine total count`
            );

            const totalCount = largeSample ? largeSample.length : data.length;
            console.log(`📊 Total borrower contacts available: ${totalCount}`);
            return totalCount;
        }

        return 0;
    }

    // Get borrower contact IDs using the selector endpoint - optimized for large batches
    async getBorrowerContactIds(token, limit = 1000) {
        console.log(`🔥 Fetching ${limit} borrower contacts in large batch...`);

        const data = await this.makeApiCall(
            `${baseUrl}/encompass/v1/borrowerContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 1, limit: limit }
            },
            `Getting ${limit} borrower contact IDs using selector (large batch)`
        );

        if (data && data.length > 0) {
            console.log(`✅ Successfully fetched ${data.length} borrower contacts via selector`);
        }

        return data || [];
    }

    // Get total count of business contacts available
    async getBusinessContactsCount(token) {
        console.log(`📊 Getting total count of business contacts...`);

        // First try the business contact selector endpoint
        let data = await this.makeApiCall(
            `${baseUrl}/encompass/v1/businessContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 1, limit: 10000 } // Try to get max possible
            },
            `Getting total business contacts count via selector`
        );

        if (data && data.length > 0) {
            console.log(`📊 Total business contacts available: ${data.length}`);
            return data.length;
        }

        // If selector doesn't work, try search approach to estimate
        console.log('   ⚠️ Business contact selector failed, trying search approach for count...');

        let totalEstimate = 0;
        const batchSize = 100;
        let currentStart = 0;

        // Try a few batches to estimate total
        for (let i = 0; i < 10; i++) { // Check up to 1000 contacts to estimate
            const batchData = await this.makeApiCall(
                `${baseUrl}/encompass/v1/businessContacts`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: {
                        start: currentStart,
                        limit: batchSize,
                        filter: {
                            terms: [
                                {
                                    canonicalName: "Fields.Log.MS.Name",
                                    value: "",
                                    matchType: "exact",
                                    include: false
                                }
                            ]
                        },
                        fields: ["id"]
                    }
                },
                `Estimating business contacts count - batch ${i + 1}`
            );

            if (!batchData || batchData.length === 0) {
                break;
            }

            totalEstimate += batchData.length;
            currentStart += batchSize;

            if (batchData.length < batchSize) {
                break; // We've reached the end
            }

            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log(`📊 Estimated business contacts available: ${totalEstimate}+ (partial count)`);
        return totalEstimate;
    }

    // Get business contact IDs using selector endpoint - optimized for large batches
    async getBusinessContactIds(token, limit = 1000) {
        console.log(`🔥 Fetching ${limit} business contacts in large batch...`);

        // First try the business contact selector endpoint with large limit
        let data = await this.makeApiCall(
            `${baseUrl}/encompass/v1/businessContactSelector`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: { start: 1, limit: limit }
            },
            `Getting ${limit} business contact IDs using selector (large batch)`
        );

        // If we got data, return it
        if (data && data.length > 0) {
            console.log(`✅ Successfully fetched ${data.length} business contacts via selector`);
            return data;
        }

        // If selector doesn't work, try pagination approach
        console.log('   ⚠️ Business contact selector failed, trying paginated approach...');

        let allContacts = [];
        const batchSize = 100; // Smaller batches for search approach
        let currentStart = 0;

        while (allContacts.length < limit) {
            const remainingLimit = Math.min(batchSize, limit - allContacts.length);

            const batchData = await this.makeApiCall(
                `${baseUrl}/encompass/v1/businessContacts`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: {
                        start: currentStart,
                        limit: remainingLimit,
                        filter: {
                            terms: [
                                {
                                    canonicalName: "Fields.Log.MS.Name",
                                    value: "",
                                    matchType: "exact",
                                    include: false // This should return all non-empty names
                                }
                            ]
                        },
                        fields: ["id", "name", "contactName", "email", "phone"]
                    }
                },
                `Searching business contacts batch ${Math.floor(currentStart/batchSize) + 1}`
            );

            if (!batchData || batchData.length === 0) {
                console.log(`   ⚠️ No more business contacts found at start position ${currentStart}`);
                break;
            }

            allContacts.push(...batchData);
            currentStart += batchSize;

            // Small delay between batches
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log(`✅ Fetched ${allContacts.length} business contacts via paginated search`);
        return allContacts.slice(0, limit);
    }

    // Get detailed borrower contact information
    async getBorrowerContactDetails(contactId, token) {
        return await this.makeApiCall(
            `${baseUrl}/encompass/v1/borrowerContacts/${contactId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting borrower contact details for ${contactId}`
        );
    }

    // Get loan folders to find loans with borrower data (simplified request)
    async getLoanFolders(token, limit = 25) {
        console.log(`⚠️ Attempting loan folders API (may require special permissions)...`);
        const data = await this.makeApiCall(
            `${baseUrl}/encompass/v1/loanFolders`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: {
                    filter: { terms: [] },
                    fields: ["Loan.LoanNumber", "Loan.GUID"],
                    start: 0,
                    limit: limit
                }
            },
            `Getting ${limit} loan folders (simplified)`
        );
        return data || [];
    }

    // Get detailed loan information
    async getLoanDetails(loanId, token) {
        return await this.makeApiCall(
            `${baseUrl}/encompass/v1/loans/${loanId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting loan details for ${loanId}`
        );
    }

    // Get loan associates (loan originators, etc.)
    async getLoanAssociates(loanId, token) {
        return await this.makeApiCall(
            `${baseUrl}/encompass/v1/loans/${loanId}/associates`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting loan associates for ${loanId}`
        );
    }

    // Get business contact details (for realtors)
    async getBusinessContactDetails(contactId, token) {
        return await this.makeApiCall(
            `${baseUrl}/encompass/v1/businessContacts/${contactId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            },
            `Getting business contact details for ${contactId}`
        );
    }

    // Get total count of ALL contacts in Encompass
    async getAllContactsCount(token) {
        console.log(`\n🔍 DISCOVERING TOTAL CONTACTS IN ENCOMPASS...`);
        console.log('='.repeat(70));

        try {
            // Get borrower contacts count
            const borrowerCount = await this.getBorrowerContactsCount(token);

            // Get business contacts count
            const businessCount = await this.getBusinessContactsCount(token);

            const totalCount = borrowerCount + businessCount;

            console.log('📊 ENCOMPASS CONTACT INVENTORY:');
            console.log(`   👤 Borrower Contacts: ${borrowerCount.toLocaleString()}`);
            console.log(`   🏢 Business Contacts: ${businessCount.toLocaleString()}`);
            console.log(`   🎯 TOTAL CONTACTS: ${totalCount.toLocaleString()}`);
            console.log('='.repeat(70));

            return {
                borrowerCount,
                businessCount,
                totalCount
            };

        } catch (error) {
            console.error('❌ Error getting total contacts count:', error.message);
            return {
                borrowerCount: 0,
                businessCount: 0,
                totalCount: 0
            };
        }
    }

    // Fetch all contacts (borrower + business) with comprehensive data - optimized for large batches
    async fetchAllContacts(token, recordLimit = 1000) {
        console.log(`🔄 Fetching all contacts (borrower + business) - LARGE BATCH MODE...`);
        const allContacts = [];

        try {
            // First, get total count of all contacts available
            const totalCounts = await this.getAllContactsCount(token);

            console.log(`\n🎯 FETCH PLAN:`);
            console.log(`   📥 Requesting: ${recordLimit.toLocaleString()} contacts`);
            console.log(`   📊 Available: ${totalCounts.totalCount.toLocaleString()} total contacts`);
            console.log(`   📈 Coverage: ${((recordLimit / totalCounts.totalCount) * 100).toFixed(1)}% of all contacts`);
            console.log('');

            // Calculate split for borrower vs business contacts
            const borrowerLimit = Math.floor(recordLimit / 2);
            const businessLimit = recordLimit - borrowerLimit;

            // Fetch borrower contacts in large batch
            console.log('\n👤 Fetching borrower contacts...');
            const borrowerContacts = await this.getBorrowerContactIds(token, borrowerLimit);
            console.log(`✅ Retrieved ${borrowerContacts.length} borrower contacts`);

            // Process borrower contacts with progress tracking
            console.log(`\n🔄 Processing ${Math.min(borrowerContacts.length, borrowerLimit)} borrower contacts...`);
            for (let i = 0; i < Math.min(borrowerContacts.length, borrowerLimit); i++) {
                const contact = borrowerContacts[i];

                // Show progress every 50 contacts
                if (i % 50 === 0 || i === Math.min(borrowerContacts.length, borrowerLimit) - 1) {
                    console.log(`   📊 Processing borrower ${i + 1}/${Math.min(borrowerContacts.length, borrowerLimit)}: ${contact.id}`);
                }

                const detailedContact = await this.getBorrowerContactDetails(contact.id, token);
                if (detailedContact) {
                    allContacts.push({
                        type: 'borrower',
                        id: detailedContact.id,
                        name: `${detailedContact.firstName || ''} ${detailedContact.lastName || ''}`.trim(),
                        firstName: detailedContact.firstName,
                        lastName: detailedContact.lastName,
                        email: detailedContact.personalEmail || detailedContact.businessEmail,
                        phone: detailedContact.homePhone || detailedContact.workPhone || detailedContact.mobilePhone,
                        dateOfBirth: detailedContact.birthdate,
                        address: detailedContact.currentMailingAddress,
                        rawData: detailedContact
                    });
                }
            }

            // Fetch business contacts in large batch
            console.log('\n🏢 Fetching business contacts...');
            const businessContacts = await this.getBusinessContactIds(token, businessLimit);
            console.log(`✅ Retrieved ${businessContacts.length} business contacts`);

            // Process business contacts with progress tracking
            console.log(`\n🔄 Processing ${Math.min(businessContacts.length, businessLimit)} business contacts...`);
            for (let i = 0; i < Math.min(businessContacts.length, businessLimit); i++) {
                const contact = businessContacts[i];

                // Show progress every 50 contacts
                if (i % 50 === 0 || i === Math.min(businessContacts.length, businessLimit) - 1) {
                    console.log(`   📊 Processing business ${i + 1}/${Math.min(businessContacts.length, businessLimit)}: ${contact.id}`);
                }

                const detailedContact = await this.getBusinessContactDetails(contact.id, token);
                if (detailedContact) {
                    allContacts.push({
                        type: 'business',
                        id: detailedContact.id,
                        name: detailedContact.name || detailedContact.contactName,
                        contactName: detailedContact.contactName,
                        email: detailedContact.email,
                        phone: detailedContact.phone,
                        businessName: detailedContact.name,
                        category: detailedContact.category,
                        rawData: detailedContact
                    });
                }
            }

            console.log(`\n🎉 Total contacts fetched: ${allContacts.length}`);
            return {
                contacts: allContacts,
                totalCounts: totalCounts
            };

        } catch (error) {
            console.error('❌ Error fetching all contacts:', error.message);
            return {
                contacts: allContacts,
                totalCounts: { borrowerCount: 0, businessCount: 0, totalCount: 0 }
            };
        }
    }

    // Extract comprehensive data from multiple sources
    async extractComprehensiveData(borrowerContact, loanData, loanAssociates) {
        const comprehensiveRecord = {
            // Borrower Information (from borrower contacts API)
            borrower: {
                id: borrowerContact.id,
                name: `${borrowerContact.firstName || ''} ${borrowerContact.lastName || ''}`.trim(),
                firstName: borrowerContact.firstName,
                lastName: borrowerContact.lastName,
                email: borrowerContact.personalEmail || borrowerContact.businessEmail,
                phones: {
                    home: borrowerContact.homePhone,
                    work: borrowerContact.workPhone,
                    mobile: borrowerContact.mobilePhone
                },
                dateOfBirth: borrowerContact.birthdate,
                address: borrowerContact.currentMailingAddress
            },
            
            // Loan Information (from loan APIs)
            loan: {
                id: loanData?.loanNumber || null,
                guid: loanData?.guid || null,
                interestRate: null,
                closingDate: null,
                propertyAddress: null,
                loanOriginator: null
            },
            
            // Realtor Information (basic from referral field, enhanced if business contact found)
            realtor: {
                name: borrowerContact.referral || null,
                phone: null,
                email: null,
                businessContactId: null
            },
            
            // Data source tracking
            dataSources: {
                borrowerContact: !!borrowerContact,
                loanData: !!loanData,
                loanAssociates: !!loanAssociates,
                realtorBusinessContact: false
            }
        };

        // Extract loan-specific data if available
        if (loanData) {
            // Try to extract interest rate from various possible field locations
            comprehensiveRecord.loan.interestRate = 
                loanData.fields?.['4'] || 
                loanData.interestRate || 
                loanData.noteRate || 
                null;
                
            // Try to extract closing date from various possible field locations
            comprehensiveRecord.loan.closingDate = 
                loanData.fields?.['763'] || 
                loanData.closingDate || 
                loanData.estimatedClosingDate || 
                null;
                
            // Try to extract property address
            comprehensiveRecord.loan.propertyAddress = 
                loanData.fields?.['11'] || 
                loanData.propertyAddress || 
                loanData.subjectPropertyAddress || 
                null;
        }

        // Extract loan originator information from associates
        if (loanAssociates && Array.isArray(loanAssociates)) {
            const loanOriginator = loanAssociates.find(associate => 
                associate.roleType === 'LoanOfficer' || 
                associate.roleType === 'LoanOriginator' ||
                associate.role?.toLowerCase().includes('originator') ||
                associate.role?.toLowerCase().includes('officer')
            );
            
            if (loanOriginator) {
                comprehensiveRecord.loan.loanOriginator = {
                    name: loanOriginator.name || `${loanOriginator.firstName || ''} ${loanOriginator.lastName || ''}`.trim(),
                    email: loanOriginator.email,
                    phone: loanOriginator.phone,
                    role: loanOriginator.role || loanOriginator.roleType,
                    id: loanOriginator.id
                };
            }
        }

        return comprehensiveRecord;
    }

    // Save comprehensive data to JSON file
    saveDataToFile(data, filename) {
        try {
            const timestamp = Date.now();
            const fullFilename = filename || `encompass-comprehensive-data-${timestamp}.json`;
            const filepath = path.join(dataDir, fullFilename);

            const dataToSave = {
                metadata: {
                    totalRecords: data.length,
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    apiCallsMade: this.apiCallCount,
                    source: 'Encompass API - Multiple Endpoints',
                    endpoints: [
                        'POST /encompass/v1/borrowerContactSelector',
                        'GET /encompass/v1/borrowerContacts/{contactId}',
                        'POST /encompass/v1/loanFolders',
                        'GET /encompass/v1/loans/{loanId}',
                        'GET /encompass/v1/loans/{loanId}/associates',
                        'GET /encompass/v1/businessContacts/{contactId}'
                    ]
                },
                comprehensiveData: data
            };

            fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
            console.log(`💾 Comprehensive data saved to: ${fullFilename}`);
            console.log(`📁 Full path: ${filepath}`);
            return filepath;
        } catch (error) {
            console.error('❌ Error saving data:', error.message);
            return null;
        }
    }

    // Main execution function to fetch all contacts - optimized for large batches
    async runAllContactsFetch(recordLimit = 1000) {
        console.log(`🚀 Starting LARGE BATCH contacts fetch for ${recordLimit.toLocaleString()} records...\n`);
        console.log(`🔥 LARGE BATCH MODE: Fetching ${Math.floor(recordLimit/2)} borrower + ${recordLimit - Math.floor(recordLimit/2)} business contacts`);

        try {
            const token = await getAccessToken();

            // Fetch all contacts (borrower + business) in large batch
            const result = await this.fetchAllContacts(token, recordLimit);
            const allContacts = result.contacts || result; // Handle both return formats
            const totalCounts = result.totalCounts || { borrowerCount: 0, businessCount: 0, totalCount: 0 };

            // Save contacts data with total counts metadata
            console.log('\n💾 Saving large batch contacts data...');
            const filename = `encompass-all-contacts-LARGE-BATCH-${recordLimit}-records-${Date.now()}.json`;
            const filepath = this.saveContactsToFileWithCounts(allContacts, totalCounts, filename);

            // Analysis and summary
            console.log('\n📊 Large Batch Contacts Analysis...');
            this.analyzeContactsData(allContacts);

            console.log('\n🎉 LARGE BATCH contacts fetch completed successfully!');
            console.log('='.repeat(70));
            console.log(`📥 Contacts processed: ${allContacts.length.toLocaleString()}`);
            console.log(`📊 Total available in Encompass: ${totalCounts.totalCount.toLocaleString()}`);
            console.log(`📈 Coverage achieved: ${((allContacts.length / totalCounts.totalCount) * 100).toFixed(1)}%`);
            console.log(`🌐 Total API calls made: ${this.apiCallCount}`);
            console.log(`⏱️ Total time: ${((Date.now() - this.startTime) / 1000).toFixed(2)} seconds`);
            console.log(`💾 Data saved to: ${filepath}`);
            console.log(`🔥 LARGE BATCH SUCCESS: ${allContacts.length.toLocaleString()} contacts fetched!`);
            console.log('='.repeat(70));

            return {
                contacts: allContacts,
                totalCounts,
                filepath,
                apiCallCount: this.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000
            };

        } catch (error) {
            console.error('❌ Large batch contacts fetch failed:', error.message);
            throw error;
        }
    }

    // Main execution function to fetch comprehensive data
    async runComprehensiveFetch(recordLimit = 10) {
        console.log(`🚀 Starting comprehensive data fetch for ${recordLimit} records...\n`);

        try {
            const token = await getAccessToken();

            // Step 1: Get borrower contact IDs
            console.log('\n📋 Step 1: Getting borrower contact IDs...');
            const contactIds = await this.getBorrowerContactIds(token, recordLimit);
            console.log(`✅ Retrieved ${contactIds.length} contact IDs`);

            // Step 2: Get loan folders for loan data (may fail due to permissions)
            console.log('\n🏠 Step 2: Getting loan folders...');
            const loanFolders = await this.getLoanFolders(token, recordLimit);
            console.log(`✅ Retrieved ${loanFolders.length} loan folders`);

            if (loanFolders.length === 0) {
                console.log('⚠️ No loan folders retrieved - will proceed with borrower data only');
            }

            // Step 3: Process each borrower contact and try to match with loan data
            console.log('\n🔄 Step 3: Processing comprehensive data...');
            const comprehensiveData = [];

            for (let i = 0; i < Math.min(contactIds.length, recordLimit); i++) {
                const contactId = contactIds[i].id;
                console.log(`\n📄 Processing record ${i + 1}/${Math.min(contactIds.length, recordLimit)}: ${contactId}`);

                // Get borrower contact details
                const borrowerContact = await this.getBorrowerContactDetails(contactId, token);
                if (!borrowerContact) {
                    console.log(`   ❌ Failed to get borrower contact details`);
                    continue;
                }

                console.log(`   ✅ Borrower: ${borrowerContact.firstName || ''} ${borrowerContact.lastName || ''}`);

                // Try to find matching loan data
                let matchingLoan = null;
                let loanDetails = null;
                let loanAssociates = null;

                // Simple matching by borrower name (you may need more sophisticated matching)
                if (loanFolders.length > 0) {
                    matchingLoan = loanFolders.find(loan => {
                        const loanFirstName = loan.fields?.['4000'] || '';
                        const loanLastName = loan.fields?.['4002'] || '';
                        return (
                            loanFirstName.toLowerCase().includes((borrowerContact.firstName || '').toLowerCase()) ||
                            loanLastName.toLowerCase().includes((borrowerContact.lastName || '').toLowerCase())
                        );
                    });

                    if (matchingLoan) {
                        console.log(`   🏠 Found matching loan: ${matchingLoan.loanNumber}`);

                        // Get detailed loan information
                        loanDetails = await this.getLoanDetails(matchingLoan.guid, token);
                        if (loanDetails) {
                            console.log(`   📊 Retrieved loan details`);
                        }

                        // Get loan associates
                        loanAssociates = await this.getLoanAssociates(matchingLoan.guid, token);
                        if (loanAssociates) {
                            console.log(`   👥 Retrieved ${loanAssociates.length || 0} loan associates`);
                        }
                    } else {
                        console.log(`   ⚠️ No matching loan found`);
                    }
                }

                // Extract comprehensive data
                const comprehensiveRecord = await this.extractComprehensiveData(
                    borrowerContact,
                    loanDetails || matchingLoan,
                    loanAssociates
                );

                comprehensiveData.push(comprehensiveRecord);

                // Log summary of what we found
                console.log(`   📋 Data Summary:`);
                console.log(`      - Borrower: ${comprehensiveRecord.borrower.name || 'N/A'}`);
                console.log(`      - Email: ${comprehensiveRecord.borrower.email || 'N/A'}`);
                console.log(`      - Phone: ${Object.values(comprehensiveRecord.borrower.phones).filter(p => p).join(', ') || 'N/A'}`);
                console.log(`      - DOB: ${comprehensiveRecord.borrower.dateOfBirth || 'N/A'}`);
                console.log(`      - Interest Rate: ${comprehensiveRecord.loan.interestRate || 'N/A'}`);
                console.log(`      - Closing Date: ${comprehensiveRecord.loan.closingDate || 'N/A'}`);
                console.log(`      - Loan Originator: ${comprehensiveRecord.loan.loanOriginator?.name || 'N/A'}`);
                console.log(`      - Realtor: ${comprehensiveRecord.realtor.name || 'N/A'}`);
            }

            // Step 4: Save comprehensive data
            console.log('\n💾 Step 4: Saving comprehensive data...');
            const filename = `encompass-comprehensive-${recordLimit}-records-${Date.now()}.json`;
            const filepath = this.saveDataToFile(comprehensiveData, filename);

            // Step 5: Analysis and summary
            console.log('\n📊 Step 5: Data Analysis...');
            this.analyzeComprehensiveData(comprehensiveData);

            console.log('\n🎉 Comprehensive data fetch completed successfully!');
            console.log('='.repeat(70));
            console.log(`📥 Records processed: ${comprehensiveData.length}`);
            console.log(`🌐 Total API calls made: ${this.apiCallCount}`);
            console.log(`⏱️ Total time: ${((Date.now() - this.startTime) / 1000).toFixed(2)} seconds`);
            console.log(`💾 Data saved to: ${filepath}`);
            console.log('='.repeat(70));

            return {
                records: comprehensiveData,
                filepath,
                apiCallCount: this.apiCallCount,
                elapsedTime: (Date.now() - this.startTime) / 1000
            };

        } catch (error) {
            console.error('❌ Comprehensive fetch failed:', error.message);
            throw error;
        }
    }

    // Analyze the comprehensive data
    analyzeComprehensiveData(data) {
        const analysis = {
            totalRecords: data.length,
            withEmail: 0,
            withPhone: 0,
            withDOB: 0,
            withInterestRate: 0,
            withClosingDate: 0,
            withLoanOriginator: 0,
            withRealtor: 0,
            withLoanData: 0
        };

        data.forEach(record => {
            if (record.borrower.email) analysis.withEmail++;
            if (Object.values(record.borrower.phones).some(p => p)) analysis.withPhone++;
            if (record.borrower.dateOfBirth) analysis.withDOB++;
            if (record.loan.interestRate) analysis.withInterestRate++;
            if (record.loan.closingDate) analysis.withClosingDate++;
            if (record.loan.loanOriginator) analysis.withLoanOriginator++;
            if (record.realtor.name) analysis.withRealtor++;
            if (record.dataSources.loanData) analysis.withLoanData++;
        });

        console.log('📈 Data Completeness Analysis:');
        console.log(`   ✅ Records with email: ${analysis.withEmail}/${analysis.totalRecords} (${((analysis.withEmail/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   ✅ Records with phone: ${analysis.withPhone}/${analysis.totalRecords} (${((analysis.withPhone/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   ✅ Records with DOB: ${analysis.withDOB}/${analysis.totalRecords} (${((analysis.withDOB/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   🎯 Records with interest rate: ${analysis.withInterestRate}/${analysis.totalRecords} (${((analysis.withInterestRate/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   🎯 Records with closing date: ${analysis.withClosingDate}/${analysis.totalRecords} (${((analysis.withClosingDate/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   🎯 Records with loan originator: ${analysis.withLoanOriginator}/${analysis.totalRecords} (${((analysis.withLoanOriginator/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   🎯 Records with realtor: ${analysis.withRealtor}/${analysis.totalRecords} (${((analysis.withRealtor/analysis.totalRecords)*100).toFixed(1)}%)`);
        console.log(`   🏠 Records with loan data: ${analysis.withLoanData}/${analysis.totalRecords} (${((analysis.withLoanData/analysis.totalRecords)*100).toFixed(1)}%)`);

        return analysis;
    }

    // Save contacts data to JSON file
    saveContactsToFile(contacts, filename) {
        try {
            const timestamp = Date.now();
            const fullFilename = filename || `encompass-all-contacts-${timestamp}.json`;
            const filepath = path.join(dataDir, fullFilename);

            const dataToSave = {
                metadata: {
                    totalContacts: contacts.length,
                    borrowerContacts: contacts.filter(c => c.type === 'borrower').length,
                    businessContacts: contacts.filter(c => c.type === 'business').length,
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    apiCallsMade: this.apiCallCount,
                    source: 'Encompass API - Contacts Endpoints',
                    endpoints: [
                        'POST /encompass/v1/borrowerContacts',
                        'GET /encompass/v1/borrowerContacts/{contactId}',
                        'POST /encompass/v1/businessContacts',
                        'GET /encompass/v1/businessContacts/{contactId}'
                    ]
                },
                contacts: contacts
            };

            fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
            console.log(`💾 Contacts data saved to: ${fullFilename}`);
            console.log(`📁 Full path: ${filepath}`);
            return filepath;
        } catch (error) {
            console.error('❌ Error saving contacts data:', error.message);
            return null;
        }
    }

    // Save contacts data with total counts to JSON file
    saveContactsToFileWithCounts(contacts, totalCounts, filename) {
        try {
            const timestamp = Date.now();
            const fullFilename = filename || `encompass-all-contacts-with-counts-${timestamp}.json`;
            const filepath = path.join(dataDir, fullFilename);

            const dataToSave = {
                metadata: {
                    fetchedContacts: contacts.length,
                    borrowerContactsFetched: contacts.filter(c => c.type === 'borrower').length,
                    businessContactsFetched: contacts.filter(c => c.type === 'business').length,
                    totalAvailableInEncompass: {
                        borrowerContacts: totalCounts.borrowerCount,
                        businessContacts: totalCounts.businessCount,
                        totalContacts: totalCounts.totalCount
                    },
                    coverage: {
                        borrowerCoverage: totalCounts.borrowerCount > 0 ?
                            ((contacts.filter(c => c.type === 'borrower').length / totalCounts.borrowerCount) * 100).toFixed(2) + '%' : '0%',
                        businessCoverage: totalCounts.businessCount > 0 ?
                            ((contacts.filter(c => c.type === 'business').length / totalCounts.businessCount) * 100).toFixed(2) + '%' : '0%',
                        totalCoverage: totalCounts.totalCount > 0 ?
                            ((contacts.length / totalCounts.totalCount) * 100).toFixed(2) + '%' : '0%'
                    },
                    fetchedAt: new Date().toISOString(),
                    elapsedTime: (Date.now() - this.startTime) / 1000,
                    apiCallsMade: this.apiCallCount,
                    source: 'Encompass API - Contacts Endpoints with Full Count Discovery',
                    endpoints: [
                        'POST /encompass/v1/borrowerContactSelector',
                        'GET /encompass/v1/borrowerContacts/{contactId}',
                        'POST /encompass/v1/businessContactSelector',
                        'GET /encompass/v1/businessContacts/{contactId}'
                    ]
                },
                contacts: contacts
            };

            fs.writeFileSync(filepath, JSON.stringify(dataToSave, null, 2));
            console.log(`💾 Contacts data with full counts saved to: ${fullFilename}`);
            console.log(`📁 Full path: ${filepath}`);
            return filepath;
        } catch (error) {
            console.error('❌ Error saving contacts data with counts:', error.message);
            return null;
        }
    }

    // Analyze contacts data
    analyzeContactsData(contacts) {
        const analysis = {
            totalContacts: contacts.length,
            borrowerContacts: contacts.filter(c => c.type === 'borrower').length,
            businessContacts: contacts.filter(c => c.type === 'business').length,
            withEmail: contacts.filter(c => c.email).length,
            withPhone: contacts.filter(c => c.phone).length,
            withName: contacts.filter(c => c.name && c.name.trim()).length
        };

        console.log('📈 Contacts Analysis:');
        console.log(`   📊 Total contacts: ${analysis.totalContacts}`);
        console.log(`   👤 Borrower contacts: ${analysis.borrowerContacts} (${((analysis.borrowerContacts/analysis.totalContacts)*100).toFixed(1)}%)`);
        console.log(`   🏢 Business contacts: ${analysis.businessContacts} (${((analysis.businessContacts/analysis.totalContacts)*100).toFixed(1)}%)`);
        console.log(`   ✅ Contacts with email: ${analysis.withEmail}/${analysis.totalContacts} (${((analysis.withEmail/analysis.totalContacts)*100).toFixed(1)}%)`);
        console.log(`   ✅ Contacts with phone: ${analysis.withPhone}/${analysis.totalContacts} (${((analysis.withPhone/analysis.totalContacts)*100).toFixed(1)}%)`);
        console.log(`   ✅ Contacts with name: ${analysis.withName}/${analysis.totalContacts} (${((analysis.withName/analysis.totalContacts)*100).toFixed(1)}%)`);

        return analysis;
    }

    // ==================== LOAN DATA METHODS ====================

    async getLoansByBorrower(borrowerId, token) {
        try {
            // Try to get loans associated with this borrower
            const data = await this.makeApiCall(
                `${baseUrl}/encompass/v1/loans`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: {
                        filter: {
                            terms: [
                                {
                                    canonicalName: "Loan.BorrowerId",
                                    value: borrowerId,
                                    matchType: "exact"
                                }
                            ]
                        },
                        fields: ["Loan.LoanNumber", "Loan.GUID", "Fields.4", "Fields.763"]
                    }
                },
                `Getting loans for borrower ${borrowerId}`
            );

            return data || [];
        } catch (error) {
            console.log(`⚠️ Could not get loans for borrower ${borrowerId}: ${error.message}`);
            return [];
        }
    }

    async getLoanDetails(loanId, token) {
        try {
            const data = await this.makeApiCall(
                `${baseUrl}/encompass/v1/loans/${loanId}`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                },
                `Getting loan details for ${loanId}`
            );

            return data;
        } catch (error) {
            console.log(`⚠️ Could not get loan details for ${loanId}: ${error.message}`);
            return null;
        }
    }

    async getLoanAssociates(loanId, token) {
        try {
            const data = await this.makeApiCall(
                `${baseUrl}/encompass/v1/loans/${loanId}/associates`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                },
                `Getting loan associates for ${loanId}`
            );

            return data || [];
        } catch (error) {
            console.log(`⚠️ Could not get loan associates for ${loanId}: ${error.message}`);
            return [];
        }
    }

    async findRealtorByName(realtorName, token) {
        try {
            // Clean up the realtor name (remove "realtor" suffix, extra spaces, etc.)
            const cleanName = realtorName.toLowerCase()
                .replace(/\brealtor\b/g, '')
                .replace(/\bagent\b/g, '')
                .trim();

            console.log(`🔍 Searching business contacts for realtor: "${cleanName}"`);

            // Get all business contacts and search for the realtor
            const businessContacts = await this.getBusinessContactIds(token, 1000);

            for (const contact of businessContacts) {
                const details = await this.getBusinessContactDetails(contact.id, token);
                if (details) {
                    const contactName = (details.name || details.contactName || '').toLowerCase();
                    const firstName = (details.firstName || '').toLowerCase();
                    const lastName = (details.lastName || '').toLowerCase();
                    const fullName = `${firstName} ${lastName}`.trim();

                    // Check if any of the contact names match the realtor name
                    if (contactName.includes(cleanName) ||
                        fullName.includes(cleanName) ||
                        cleanName.includes(contactName) ||
                        cleanName.includes(fullName)) {

                        console.log(`✅ Found matching realtor: ${details.name || details.contactName}`);
                        return details;
                    }
                }
            }

            return null;
        } catch (error) {
            console.log(`⚠️ Error searching for realtor ${realtorName}: ${error.message}`);
            return null;
        }
    }

    // Enhanced extractComprehensiveData method
    extractComprehensiveData(borrowerContact, loanData = null, loanAssociates = null, realtorData = null) {
        const comprehensiveRecord = {
            // Borrower Information (from borrower contacts API)
            borrower: {
                id: borrowerContact.id,
                name: `${borrowerContact.firstName || ''} ${borrowerContact.lastName || ''}`.trim(),
                firstName: borrowerContact.firstName,
                lastName: borrowerContact.lastName,
                email: borrowerContact.personalEmail || borrowerContact.businessEmail || borrowerContact.primaryEmail?.trim(),
                phones: {
                    home: borrowerContact.homePhone,
                    work: borrowerContact.workPhone,
                    mobile: borrowerContact.mobilePhone,
                    primary: borrowerContact.primaryPhone
                },
                dateOfBirth: borrowerContact.birthdate,
                address: borrowerContact.currentMailingAddress,
                businessAddress: borrowerContact.bizAddress,
                employer: {
                    name: borrowerContact.employerName,
                    jobTitle: borrowerContact.jobTitle
                },
                borrowerType: borrowerContact.borrowerType,
                salutation: borrowerContact.salutation,
                faxNumber: borrowerContact.faxNumber,
                businessWebUrl: borrowerContact.businessWebUrl,
                ownerId: borrowerContact.ownerId,
                accessLevel: borrowerContact.accessLevel
            },

            // Loan Information (from loan APIs when available)
            loan: {
                id: loanData?.loanNumber || null,
                guid: loanData?.guid || null,
                interestRate: null,
                closingDate: null,
                propertyAddress: null,
                loanOriginator: null,
                amount: null,
                type: null
            },

            // Enhanced Realtor Information
            realtor: {
                name: borrowerContact.referral || null,
                phone: realtorData?.phone || realtorData?.workPhone || realtorData?.mobilePhone || null,
                email: realtorData?.email || realtorData?.businessEmail || realtorData?.personalEmail || null,
                businessContactId: realtorData?.id || null,
                businessName: realtorData?.name || null,
                category: realtorData?.category || null,
                address: realtorData?.address || null,
                website: realtorData?.businessWebUrl || null
            },

            // Data source tracking
            dataSources: {
                borrowerContact: !!borrowerContact,
                loanData: !!loanData,
                loanAssociates: !!loanAssociates,
                realtorBusinessContact: !!realtorData
            },

            // Metadata
            metadata: {
                extractedAt: new Date().toISOString(),
                source: 'Encompass API - Enhanced',
                version: '2.0',
                hasRealtorData: !!realtorData,
                hasLoanData: !!loanData
            }
        };

        // Extract loan-specific data if available
        if (loanData) {
            // Try to extract interest rate from various possible field locations
            comprehensiveRecord.loan.interestRate =
                loanData.fields?.['4'] ||
                loanData.interestRate ||
                loanData.noteRate ||
                null;

            // Try to extract closing date from various possible field locations
            comprehensiveRecord.loan.closingDate =
                loanData.fields?.['763'] ||
                loanData.closingDate ||
                loanData.estimatedClosingDate ||
                null;

            // Try to extract property address
            comprehensiveRecord.loan.propertyAddress =
                loanData.fields?.['11'] ||
                loanData.propertyAddress ||
                loanData.subjectPropertyAddress ||
                null;

            // Try to extract loan amount
            comprehensiveRecord.loan.amount =
                loanData.fields?.['1109'] ||
                loanData.loanAmount ||
                loanData.requestedLoanAmount ||
                null;

            // Try to extract loan type
            comprehensiveRecord.loan.type =
                loanData.fields?.['1172'] ||
                loanData.loanType ||
                loanData.loanPurpose ||
                null;
        }

        // Extract loan originator information from associates
        if (loanAssociates && Array.isArray(loanAssociates)) {
            const loanOriginator = loanAssociates.find(associate =>
                associate.roleType === 'LoanOfficer' ||
                associate.roleType === 'LoanOriginator' ||
                associate.role?.toLowerCase().includes('originator') ||
                associate.role?.toLowerCase().includes('officer')
            );

            if (loanOriginator) {
                comprehensiveRecord.loan.loanOriginator = {
                    name: loanOriginator.name || `${loanOriginator.firstName || ''} ${loanOriginator.lastName || ''}`.trim(),
                    email: loanOriginator.email,
                    phone: loanOriginator.phone,
                    role: loanOriginator.role || loanOriginator.roleType,
                    id: loanOriginator.id
                };
            }
        }

        return comprehensiveRecord;
    }

    // ==================== GOHIGHLEVEL SYNC METHODS ====================

    async syncContactToGHL(encompassContact, type = 'borrower', comprehensiveData = null) {
        try {
            let ghlContact;

            // Use comprehensive data transformation if available, otherwise use basic transformation
            if (comprehensiveData) {
                ghlContact = transformComprehensiveToGHL(comprehensiveData);
                console.log(`📊 Using comprehensive data for ${ghlContact.name}`);
            } else {
                ghlContact = transformToGHLFormat(encompassContact, type);
                console.log(`📋 Using basic data for ${ghlContact.name}`);
            }

            // Skip if no email or phone
            if (!ghlContact.email && !ghlContact.phone) {
                console.log(`⚠️ Skipping contact ${ghlContact.name} - no email or phone`);
                return { status: 'skipped', reason: 'no_contact_info' };
            }

            // Check if contact already exists in GHL
            const existingContacts = await searchGHLContact(ghlContact.email, ghlContact.phone);

            if (existingContacts.length > 0) {
                // Update existing contact
                const existingContact = existingContacts[0];
                const result = await updateGHLContact(existingContact.id, ghlContact);
                return { status: 'updated', contact: result };
            } else {
                // Create new contact
                const result = await createGHLContact(ghlContact);
                return { status: 'created', contact: result };
            }
        } catch (error) {
            console.error(`❌ Error syncing contact to GHL:`, error.message);
            return { status: 'error', error: error.message };
        }
    }

    async getComprehensiveContactData(contactId, token, type = 'borrower') {
        try {
            // Get detailed contact information
            let detailedContact;
            if (type === 'borrower') {
                detailedContact = await this.getBorrowerContactDetails(contactId, token);
            } else {
                detailedContact = await this.getBusinessContactDetails(contactId, token);
            }

            if (!detailedContact) {
                console.log(`⚠️ No contact details found for ${contactId}`);
                return null;
            }

            // For borrowers, try to get comprehensive data with loan information
            if (type === 'borrower') {
                try {
                    // Try to find associated loans for this borrower
                    const loans = await this.getLoansByBorrower(contactId, token);
                    let loanData = null;
                    let loanAssociates = null;

                    if (loans && loans.length > 0) {
                        console.log(`📋 Found ${loans.length} loans for borrower ${contactId}`);
                        // Get details for the first/most recent loan
                        const primaryLoan = loans[0];
                        loanData = await this.getLoanDetails(primaryLoan.loanNumber || primaryLoan.guid, token);
                        loanAssociates = await this.getLoanAssociates(primaryLoan.loanNumber || primaryLoan.guid, token);
                    } else {
                        console.log(`📋 No loans found for borrower ${contactId}`);
                    }

                    // Extract comprehensive data - this should always work even without loan data
                    try {
                        const comprehensiveData = this.extractComprehensiveData(detailedContact, loanData, loanAssociates);
                        return { contact: detailedContact, comprehensive: comprehensiveData };
                    } catch (extractError) {
                        console.log(`⚠️ Error extracting comprehensive data for ${contactId}: ${extractError.message}`);
                        // Return basic contact data if extraction fails
                        return { contact: detailedContact, comprehensive: null };
                    }

                } catch (loanError) {
                    console.log(`⚠️ Could not get loan data for borrower ${contactId}: ${loanError.message}`);
                    // Still try to extract basic comprehensive data
                    try {
                        const comprehensiveData = this.extractComprehensiveData(detailedContact, null, null);
                        return { contact: detailedContact, comprehensive: comprehensiveData };
                    } catch (extractError) {
                        console.log(`⚠️ Error extracting basic comprehensive data for ${contactId}: ${extractError.message}`);
                        return { contact: detailedContact, comprehensive: null };
                    }
                }
            }

            // For business contacts, return basic data
            return { contact: detailedContact, comprehensive: null };

        } catch (error) {
            console.error(`❌ Error getting comprehensive contact data for ${contactId}:`, error.message);
            return null;
        }
    }

    async runIncrementalSync() {
        console.log('\n🔄 Starting incremental sync to GoHighLevel...');
        console.log('='.repeat(70));

        const syncStartTime = Date.now();
        let syncResults = {
            created: 0,
            updated: 0,
            skipped: 0,
            errors: 0,
            totalProcessed: 0
        };

        try {
            const token = await getAccessToken();

            // Get total counts first
            const totalCounts = await this.getAllContactsCount(token);

            // Calculate starting point based on database
            const startFrom = this.syncDatabase.totalProcessed;
            const remainingContacts = totalCounts.totalCount - startFrom;

            console.log(`\n📊 INCREMENTAL SYNC PLAN:`);
            console.log(`   📥 Total contacts in Encompass: ${totalCounts.totalCount.toLocaleString()}`);
            console.log(`   ✅ Already processed: ${startFrom.toLocaleString()}`);
            console.log(`   🔄 Remaining to sync: ${remainingContacts.toLocaleString()}`);
            console.log('');

            if (remainingContacts <= 0) {
                console.log('✅ All contacts already synced!');
                return syncResults;
            }

            // Fetch remaining borrower contacts
            const borrowerStartFrom = Math.max(0, startFrom - totalCounts.businessCount);
            if (borrowerStartFrom < totalCounts.borrowerCount) {
                console.log(`\n👤 Syncing remaining borrower contacts...`);
                const borrowerContacts = await this.getBorrowerContactIds(token, totalCounts.borrowerCount);

                for (let i = borrowerStartFrom; i < borrowerContacts.length; i++) {
                    const contact = borrowerContacts[i];

                    if (i % 50 === 0) {
                        console.log(`   📊 Processing borrower ${i + 1}/${borrowerContacts.length}: ${contact.id}`);
                    }

                    // Get comprehensive contact data (includes loan information)
                    const contactData = await this.getComprehensiveContactData(contact.id, token, 'borrower');
                    if (contactData) {
                        const syncResult = await this.syncContactToGHL(
                            contactData.contact,
                            'borrower',
                            contactData.comprehensive
                        );

                        // Update counters
                        syncResults[syncResult.status]++;
                        syncResults.totalProcessed++;

                        // Update database
                        this.syncDatabase.totalProcessed++;
                        this.syncDatabase.processedContacts.push({
                            id: contactData.contact.id,
                            type: 'borrower',
                            syncStatus: syncResult.status,
                            hasComprehensiveData: !!contactData.comprehensive,
                            hasLoanData: !!(contactData.comprehensive?.loan?.id),
                            syncedAt: new Date().toISOString()
                        });

                        // Save database every 100 contacts
                        if (syncResults.totalProcessed % 100 === 0) {
                            saveSyncDatabase(this.syncDatabase);
                        }
                    }
                }
            }

            // Fetch remaining business contacts
            const businessStartFrom = Math.max(0, startFrom - totalCounts.borrowerCount);
            if (businessStartFrom < totalCounts.businessCount) {
                console.log(`\n🏢 Syncing remaining business contacts...`);
                const businessContacts = await this.getBusinessContactIds(token, totalCounts.businessCount);

                for (let i = businessStartFrom; i < businessContacts.length; i++) {
                    const contact = businessContacts[i];

                    if (i % 50 === 0) {
                        console.log(`   📊 Processing business ${i + 1}/${businessContacts.length}: ${contact.id}`);
                    }

                    // Get comprehensive contact data (business contacts don't have loan data)
                    const contactData = await this.getComprehensiveContactData(contact.id, token, 'business');
                    if (contactData) {
                        const syncResult = await this.syncContactToGHL(
                            contactData.contact,
                            'business',
                            contactData.comprehensive
                        );

                        // Update counters
                        syncResults[syncResult.status]++;
                        syncResults.totalProcessed++;

                        // Update database
                        this.syncDatabase.totalProcessed++;
                        this.syncDatabase.processedContacts.push({
                            id: contactData.contact.id,
                            type: 'business',
                            syncStatus: syncResult.status,
                            hasComprehensiveData: !!contactData.comprehensive,
                            syncedAt: new Date().toISOString()
                        });

                        // Save database every 100 contacts
                        if (syncResults.totalProcessed % 100 === 0) {
                            saveSyncDatabase(this.syncDatabase);
                        }
                    }
                }
            }

            // Final database save
            this.syncDatabase.lastSyncTime = new Date().toISOString();
            this.syncDatabase.syncHistory.push({
                syncTime: new Date().toISOString(),
                results: syncResults,
                elapsedTime: (Date.now() - syncStartTime) / 1000
            });
            saveSyncDatabase(this.syncDatabase);

            console.log('\n🎉 Incremental sync completed!');
            console.log('='.repeat(70));
            console.log(`📊 SYNC RESULTS:`);
            console.log(`   ✅ Created: ${syncResults.created}`);
            console.log(`   🔄 Updated: ${syncResults.updated}`);
            console.log(`   ⚠️ Skipped: ${syncResults.skipped}`);
            console.log(`   ❌ Errors: ${syncResults.errors}`);
            console.log(`   📥 Total processed: ${syncResults.totalProcessed}`);
            console.log(`   ⏱️ Time taken: ${((Date.now() - syncStartTime) / 1000).toFixed(2)} seconds`);
            console.log('='.repeat(70));

            return syncResults;

        } catch (error) {
            console.error('❌ Incremental sync failed:', error.message);
            throw error;
        }
    }
}

// ==================== CRON JOB FUNCTIONALITY ====================

function startCronJob() {
    console.log('⏰ Starting cron job for 6-hour incremental sync...');
    console.log(`📅 Schedule: ${SYNC_CONFIG.cronSchedule} (every 6 hours)`);

    cron.schedule(SYNC_CONFIG.cronSchedule, async () => {
        console.log('\n🔔 Cron job triggered - starting incremental sync...');
        const fetcher = new ComprehensiveDataFetcher();

        try {
            await fetcher.runIncrementalSync();
            console.log('✅ Scheduled sync completed successfully');
        } catch (error) {
            console.error('❌ Scheduled sync failed:', error.message);
        }
    });

    console.log('✅ Cron job started successfully');
}

// ==================== MAIN EXECUTION ====================

// Run fetch if called directly
if (require.main === module) {
    const fetcher = new ComprehensiveDataFetcher();

    // Get record limit and mode from command line arguments
    const recordLimit = parseInt(process.argv[2]) || 1000; // Default to 1000 for large batch
    const mode = process.argv[3] || 'sync'; // 'contacts', 'comprehensive', 'sync', or 'cron'

    if (mode === 'sync') {
        // Run incremental sync to GoHighLevel
        console.log('🎯 Mode: Incremental Sync to GoHighLevel');
        fetcher.runIncrementalSync().then(result => {
            console.log('\n✅ Incremental sync completed successfully!');
            console.log('📊 Final Results:', JSON.stringify({
                created: result.created,
                updated: result.updated,
                skipped: result.skipped,
                errors: result.errors,
                totalProcessed: result.totalProcessed
            }, null, 2));
        }).catch(error => {
            console.error('❌ Incremental sync failed:', error.message);
            process.exit(1);
        });
    } else if (mode === 'cron') {
        // Start cron job for scheduled syncing
        console.log('🎯 Mode: Start Cron Job');
        startCronJob();

        // Keep the process running
        console.log('🔄 Process will keep running for scheduled syncs...');
        console.log('Press Ctrl+C to stop');

        // Run initial sync
        fetcher.runIncrementalSync().then(result => {
            console.log('\n✅ Initial sync completed successfully!');
        }).catch(error => {
            console.error('❌ Initial sync failed:', error.message);
        });

    } else if (mode === 'contacts') {
        // Fetch all contacts (borrower + business)
        console.log('🎯 Mode: All Contacts Fetch');
        fetcher.runAllContactsFetch(recordLimit).then(result => {
            console.log('\n✅ All contacts fetch completed successfully!');
            console.log('📊 Final Results:', JSON.stringify({
                contactsProcessed: result.contacts.length,
                borrowerContacts: result.contacts.filter(c => c.type === 'borrower').length,
                businessContacts: result.contacts.filter(c => c.type === 'business').length,
                apiCallsMade: result.apiCallCount,
                elapsedTime: `${result.elapsedTime.toFixed(2)} seconds`,
                savedToFile: result.filepath
            }, null, 2));
        }).catch(error => {
            console.error('❌ All contacts fetch failed:', error.message);
            process.exit(1);
        });
    } else {
        // Fetch comprehensive data (original functionality)
        console.log('🎯 Mode: Comprehensive Data Fetch');
        fetcher.runComprehensiveFetch(recordLimit).then(result => {
            console.log('\n✅ Comprehensive data fetch completed successfully!');
            console.log('📊 Final Results:', JSON.stringify({
                recordsProcessed: result.records.length,
                apiCallsMade: result.apiCallCount,
                elapsedTime: `${result.elapsedTime.toFixed(2)} seconds`,
                savedToFile: result.filepath
            }, null, 2));
        }).catch(error => {
            console.error('❌ Comprehensive data fetch failed:', error.message);
            process.exit(1);
        });
    }
}

module.exports = ComprehensiveDataFetcher;

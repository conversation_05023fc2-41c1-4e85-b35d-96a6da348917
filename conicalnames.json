[{"filterOnly": false, "category": "Borrower Contact", "description": "Borrower First Name", "canonicalName": "Contact.FirstName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Borrower Last Name", "canonicalName": "Contact.LastName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Borrower Middle Name", "canonicalName": "Contact.MiddleName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Borrower Suffix", "canonicalName": "Contact.SuffixName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Primary Contact", "canonicalName": "Contact.PrimContact", "dataType": "YN", "dataSource": "BorrowerContact", "maxLength": 1}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Phone", "canonicalName": "Contact.HomePhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Work Phone", "canonicalName": "Contact.WorkPhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Cell Phone", "canonicalName": "Contact.MobilePhone", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Fax Number", "canonicalName": "Contact.FaxNumber", "dataType": "PHONE", "dataSource": "BorrowerContact", "maxLength": 17}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Email", "canonicalName": "Contact.PersonalEmail", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Work Email", "canonicalName": "Contact.BizEmail", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Do not call", "canonicalName": "Contact.DoNotCall", "dataType": "YN", "dataSource": "BorrowerContact", "maxLength": 1}, {"filterOnly": false, "category": "Borrower Contact", "description": "Do not email", "canonicalName": "Contact.DoNotSpam", "dataType": "YN", "dataSource": "BorrowerContact", "maxLength": 1}, {"filterOnly": false, "category": "Borrower Contact", "description": "Do not fax", "canonicalName": "Contact.DoNotFax", "dataType": "YN", "dataSource": "BorrowerContact", "maxLength": 1}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Address1", "canonicalName": "Contact.HomeAddress1", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Address2", "canonicalName": "Contact.HomeAddress2", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Address City", "canonicalName": "Contact.HomeCity", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Address State", "canonicalName": "Contact.HomeState", "dataType": "STATE", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Home Address Zip", "canonicalName": "Contact.HomeZip", "dataType": "ZIPCODE", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Status", "canonicalName": "Contact.Status", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Inactive", "Cold Lead", "Warm Lead", "Hot Lead", "Live Lead", "Active Loan(s)"]}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Company", "canonicalName": "Contact.EmployerName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Title", "canonicalName": "Contact.JobTitle", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Address1", "canonicalName": "Contact.BizAddress1", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Address2", "canonicalName": "Contact.BizAddress2", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business City", "canonicalName": "Contact.BizCity", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business State", "canonicalName": "Contact.BizState", "dataType": "STATE", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Zip", "canonicalName": "Contact.BizZip", "dataType": "ZIPCODE", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Business Web URL", "canonicalName": "Contact.BizWebUrl", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Birthday", "canonicalName": "Contact.Birthdate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Borrower Contact", "description": "SSN", "canonicalName": "Contact.SSN", "dataType": "SSN", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Borrower Contact", "description": "Referral", "canonicalName": "Contact.Referral", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Annual Income", "canonicalName": "Contact.Income", "dataType": "INTEGER", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Borrower Contact", "description": "Married", "canonicalName": "Contact.MaritalStatus", "dataType": "YN", "dataSource": "BorrowerContact", "maxLength": 1}, {"filterOnly": false, "category": "Borrower Contact", "description": "Spouse", "canonicalName": "Contact.SpouseName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Anniversary", "canonicalName": "Contact.Anniversary", "dataType": "MONTHDAY", "dataSource": "BorrowerContact", "maxLength": 7}, {"filterOnly": false, "category": "Borrower Contact", "description": "Contact Owner", "canonicalName": "ContactOwner.UserName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Contact Type", "canonicalName": "Contact.ContactType", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Prospect", "Client", "Lead"]}, {"filterOnly": false, "category": "Borrower Contact", "description": "Access Level", "canonicalName": "Contact.AccessLevel", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Public", "Private"]}, {"filterOnly": false, "category": "Borrower Contact", "description": "Groups", "canonicalName": "ContactGroupCount.GroupCount", "dataType": "INTEGER", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Borrower Contact", "description": "Associated Loans", "canonicalName": "ContactLoanCount.LoanCount", "dataType": "INTEGER", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Borrower Contact", "description": "Associated Complete Loans", "canonicalName": "ContactCompleteLoanCount.LoanCount", "dataType": "INTEGER", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Borrower Contact", "description": "Salutation", "canonicalName": "Contact.Salutation", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Borrower Contact", "description": "Last Modification", "canonicalName": "Contact.LastModified", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Opportunity", "description": "Opportunity Loan Amount", "canonicalName": "Opportunity.LoanAmount", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "<PERSON><PERSON>", "canonicalName": "Opportunity.Purpose", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Purchase", "Cash-Out Refi", "No Cash-Out Refi", "Construction", "Construction - Perm", "Other"]}, {"filterOnly": false, "category": "Opportunity", "description": "Other Purpose", "canonicalName": "Opportunity.PurposeOther", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Opportunity", "description": "<PERSON>an <PERSON>", "canonicalName": "Opportunity.Term", "dataType": "INTEGER", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Opportunity", "description": "Amortization", "canonicalName": "Opportunity.Amortization", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Fixed Rate", "GPM", "ARM", "Other"]}, {"filterOnly": false, "category": "Opportunity", "description": "Available Down Payment", "canonicalName": "Opportunity.DownPayment", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Current Mortgage Balance", "canonicalName": "Opportunity.MortgageBalance", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Current Mortgage Rate %", "canonicalName": "Opportunity.MortgageRate", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Monthly Payment (housing)", "canonicalName": "Opportunity.HousingPayment", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Monthly Payment (non-housing)", "canonicalName": "Opportunity.NonHousingPayment", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Property Address", "canonicalName": "Opportunity.PropertyAddress", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Opportunity", "description": "Property City", "canonicalName": "Opportunity.PropertyCity", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Opportunity", "description": "Property State", "canonicalName": "Opportunity.PropertyState", "dataType": "STATE", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Opportunity", "description": "Property Zip", "canonicalName": "Opportunity.PropertyZip", "dataType": "ZIPCODE", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Opportunity", "description": "Property Use", "canonicalName": "Opportunity.PropertyUse", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Primary", "Secondary", "Investment"]}, {"filterOnly": false, "category": "Opportunity", "description": "Property Type", "canonicalName": "Opportunity.PropertyType", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Attached", "Condominium", "Co-Operative", "Detached", "High Rise Condominium", "Manufactured Housing", "PUD"]}, {"filterOnly": false, "category": "Opportunity", "description": "Property Value", "canonicalName": "Opportunity.PropertyValue", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Cash Out", "canonicalName": "Opportunity.CashOut", "dataType": "DECIMAL_4", "dataSource": "BorrowerContact", "maxLength": 16}, {"filterOnly": false, "category": "Opportunity", "description": "Property Purchase date", "canonicalName": "Opportunity.PurchaseDate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Opportunity", "description": "Credit Rating", "canonicalName": "Opportunity.CreditRating", "dataType": "INTEGER", "dataSource": "BorrowerContact", "maxLength": 11}, {"filterOnly": false, "category": "Opportunity", "description": "Employment", "canonicalName": "Opportunity.Employment", "dataType": "STRING", "dataSource": "BorrowerContact", "options": ["Employed", "Self-Employed", "Unemployed"]}, {"filterOnly": false, "category": "Opportunity", "description": "Bankruptcy", "canonicalName": "Opportunity.Bankruptcy", "dataType": "YN", "dataSource": "BorrowerContact", "maxLength": 1}, {"filterOnly": false, "category": "Recent Events", "description": "Last Mail Merge Doc", "canonicalName": "LastMailMerge.LetterName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Mail Merge Date", "canonicalName": "LastMailMerge.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Mail Merge User ID", "canonicalName": "LastMailMerge.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Merge Doc", "canonicalName": "LastEmailMerge.LetterName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Merge Date", "canonicalName": "LastEmailMerge.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Merge User ID", "canonicalName": "LastEmailMerge.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Merge Subject", "canonicalName": "LastEmailMerge.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Call Date", "canonicalName": "LastCall.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Call User ID", "canonicalName": "LastCall.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Call Subject", "canonicalName": "LastCallNotes.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Call Details", "canonicalName": "LastCallNotes.Details", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Date", "canonicalName": "LastEmail.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email User ID", "canonicalName": "LastEmail.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Subject", "canonicalName": "LastEmailNotes.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Email Details", "canonicalName": "LastEmailNotes.Details", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Fax Date", "canonicalName": "LastFax.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Fax User ID", "canonicalName": "LastFax.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Fax Subject", "canonicalName": "LastFaxNotes.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Fax Details", "canonicalName": "LastFaxNotes.Details", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Email Date", "canonicalName": "LastCampaignEmail.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Email User ID", "canonicalName": "LastCampaignEmail.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Email Subject", "canonicalName": "LastCampaignEmail.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Email <PERSON>ment", "canonicalName": "LastCampaignEmail.LetterName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Email Status", "canonicalName": "LastCampaignEmail.CampaignActivityStatus", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Email Schedule", "canonicalName": "LastCampaignEmail.CampaignScheduledDate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Fax Date", "canonicalName": "LastCampaignFax.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Fax User ID", "canonicalName": "LastCampaignFax.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Fax Subject", "canonicalName": "LastCampaignFax.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Fax Status", "canonicalName": "LastCampaignFax.CampaignActivityStatus", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Fax Schedule", "canonicalName": "LastCampaignFax.CampaignScheduledDate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Call Date", "canonicalName": "LastCampaignCall.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Call User ID", "canonicalName": "LastCampaignCall.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Call Subject", "canonicalName": "LastCampaignCall.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Call Status", "canonicalName": "LastCampaignCall.CampaignActivityStatus", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Call Schedule", "canonicalName": "LastCampaignCall.CampaignScheduledDate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Letter Date", "canonicalName": "LastCampaignLetter.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Letter User ID", "canonicalName": "LastCampaignLetter.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Letter Document", "canonicalName": "LastCampaignLetter.LetterName", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Letter Status", "canonicalName": "LastCampaignLetter.CampaignActivityStatus", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Letter Schedule", "canonicalName": "LastCampaignLetter.CampaignScheduledDate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Reminder Date", "canonicalName": "LastCampaignReminder.TimeOfHistory", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Reminder User ID", "canonicalName": "LastCampaignReminder.Sender", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Reminder Subject", "canonicalName": "LastCampaignReminder.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Reminder Status", "canonicalName": "LastCampaignReminder.CampaignActivityStatus", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Recent Events", "description": "Last Campaign Reminder Schedule", "canonicalName": "LastCampaignReminder.CampaignScheduledDate", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Next Appointment Date", "canonicalName": "NextAppointment.StartDateTime", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Next Appointment Subject", "canonicalName": "NextAppointment.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Appointments", "description": "Next Appointment Start Time", "canonicalName": "NextAppointment.StartDateTime", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Next Appointment End Time", "canonicalName": "NextAppointment.EndDateTime", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Next Appointment Comments", "canonicalName": "NextAppointment.Description", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Appointments", "description": "Most Recent Appointment Date", "canonicalName": "LastAppointment.StartDateTime", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Most Recent Appointment Subject", "canonicalName": "LastAppointment.Subject", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "Appointments", "description": "Most Recent Appointment Start Time", "canonicalName": "LastAppointment.StartDateTime", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Most Recent Appointment End Time", "canonicalName": "LastAppointment.EndDateTime", "dataType": "DATE", "dataSource": "BorrowerContact", "maxLength": 10}, {"filterOnly": false, "category": "Appointments", "description": "Most Recent Appointment Comments", "canonicalName": "LastAppointment.Description", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "BorrowerCustomField", "description": "Lead Source", "canonicalName": "CUSTOM.LEAD SOURCE", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "BorrowerCustomField", "description": "Promo Code", "canonicalName": "CUSTOM.PROMO CODE", "dataType": "STRING", "dataSource": "BorrowerContact"}, {"filterOnly": false, "category": "BorrowerCustomField", "description": "Lead Origin", "canonicalName": "CUSTOM.LEAD ORIGIN", "dataType": "DROPDOWNLIST", "dataSource": "BorrowerContact", "options": ["Accountant", "Attorney", "Bank", "Builder", "<PERSON><PERSON><PERSON>", "Financial Advisor", "Leads360", "Networking Group", "Realtor", "Referral - Family", "Telemarketing", "Tele_01", "Union/Organization", "WebCenter", "Promo Code"]}]
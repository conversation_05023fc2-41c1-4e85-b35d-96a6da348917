require('dotenv').config();
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration from environment variables
const API_SERVER = process.env.ENCOMPASS_API_URL || 'https://api.elliemae.com';
const clientId = process.env.ENCOMPASS_CLIENT_ID;
const clientSecret = process.env.ENCOMPASS_CLIENT_SECRET;
const username = process.env.ENCOMPASS_USERNAME;
const password = process.env.ENCOMPASS_PASSWORD;

console.log('🚀 Starting Encompass Canonical Fields Fetch Script');
console.log('='.repeat(70));
console.log(`📡 API Server: ${API_SERVER}`);
console.log(`👤 Username: ${username}`);
console.log('='.repeat(70));

// Authentication function
async function getAccessToken() {
    try {
        console.log('🔐 Getting access token...');

        const tokenResponse = await axios.post(`${API_SERVER}/oauth2/v1/token`,
            `grant_type=password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        console.log('✅ Access token obtained successfully');
        return tokenResponse.data.access_token;
    } catch (error) {
        console.error('❌ Error getting access token:', error.response?.data || error.message);
        throw error;
    }
}

// Function to read canonical names from file
function loadCanonicalNames() {
    try {
        console.log('📖 Loading canonical names from conicalnames.txt...');
        const fileContent = fs.readFileSync('conicalnames.txt', 'utf8');
        const canonicalData = JSON.parse(fileContent);
        
        console.log(`✅ Loaded ${canonicalData.length} canonical field definitions`);
        
        // Extract just the canonical names for the API request
        const canonicalNames = canonicalData
            .filter(field => !field.filterOnly) // Only include fields that can be selected
            .map(field => field.canonicalName);
            
        console.log(`📋 Found ${canonicalNames.length} selectable canonical fields`);
        
        return {
            canonicalNames,
            fieldDefinitions: canonicalData
        };
    } catch (error) {
        console.error('❌ Error loading canonical names:', error.message);
        throw error;
    }
}

// Function to fetch data from endpoint with canonical fields
async function fetchDataWithCanonicalFields(url, token, description, body) {
    try {
        console.log(`\n📥 Fetching data from: ${description}`);
        console.log(`🔗 URL: ${url}`);
        console.log(`📋 Requesting ${body.fields.length} canonical fields`);
        
        const response = await axios.post(url, body, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = response.data;
        const count = Array.isArray(data) ? data.length : (data.length || 'unknown');
        
        console.log(`✅ Successfully fetched data`);
        console.log(`📊 Data count: ${count}`);
        
        return {
            url,
            description,
            count,
            data,
            body,
            fetchedAt: new Date().toISOString()
        };
    } catch (error) {
        console.error(`❌ Error fetching ${description}:`, error.response?.data || error.message);
        return {
            url,
            description,
            count: 0,
            data: null,
            body,
            error: error.response?.data || error.message,
            fetchedAt: new Date().toISOString()
        };
    }
}

// Extract comprehensive data using canonical field mapping
function extractCanonicalData(contactData, fieldDefinitions) {
    const extractedData = {
        contactId: contactData.id || 'N/A',
        basicInfo: {},
        contactDetails: {},
        addresses: {},
        employment: {},
        opportunity: {},
        recentEvents: {},
        appointments: {},
        customFields: {},
        metadata: {
            extractedAt: new Date().toISOString(),
            fieldsExtracted: 0,
            fieldsWithData: 0
        }
    };

    // Process each field definition and extract data
    fieldDefinitions.forEach(fieldDef => {
        const canonicalName = fieldDef.canonicalName;
        const category = fieldDef.category;
        const description = fieldDef.description;
        const value = contactData[canonicalName];
        
        if (value !== undefined && value !== null && value !== '') {
            extractedData.metadata.fieldsWithData++;
            
            // Categorize the data based on field category
            switch (category) {
                case 'Borrower Contact':
                    if (canonicalName.includes('Name') || canonicalName.includes('Email') || canonicalName.includes('Phone')) {
                        extractedData.contactDetails[description] = value;
                    } else if (canonicalName.includes('Address') || canonicalName.includes('City') || canonicalName.includes('State') || canonicalName.includes('Zip')) {
                        extractedData.addresses[description] = value;
                    } else if (canonicalName.includes('Employer') || canonicalName.includes('Job') || canonicalName.includes('Income')) {
                        extractedData.employment[description] = value;
                    } else {
                        extractedData.basicInfo[description] = value;
                    }
                    break;
                case 'Opportunity':
                    extractedData.opportunity[description] = value;
                    break;
                case 'Recent Events':
                    extractedData.recentEvents[description] = value;
                    break;
                case 'Appointments':
                    extractedData.appointments[description] = value;
                    break;
                case 'BorrowerCustomField':
                    extractedData.customFields[description] = value;
                    break;
                default:
                    extractedData.basicInfo[description] = value;
            }
        }
        
        extractedData.metadata.fieldsExtracted++;
    });

    return extractedData;
}

// Main function
async function main() {
    try {
        // Load canonical names from file
        const { canonicalNames, fieldDefinitions } = loadCanonicalNames();
        
        // Get access token
        const token = await getAccessToken();
        
        // Dynamic pagination - fetch up to 3 pages with canonical fields
        const MAX_PAGES = 3;
        const PAGE_SIZE = 100; // Smaller page size since we're requesting many fields
        
        console.log('\n🎯 Starting canonical fields fetch with dynamic pagination...');
        console.log(`📄 Will fetch up to ${MAX_PAGES} pages with ${PAGE_SIZE} contacts each`);
        console.log(`📋 Using ${canonicalNames.length} canonical fields`);
        console.log('='.repeat(70));

        const results = [];
        let totalCount = 0;
        let currentPage = 0;
        let hasMoreData = true;

        // Dynamic pagination loop
        while (hasMoreData && currentPage < MAX_PAGES) {
            const start = currentPage * PAGE_SIZE;
            const pageNum = currentPage + 1;
            
            const endpoint = {
                url: `${API_SERVER}/encompass/v1/borrowerContactSelector`,
                description: `Canonical Fields Fetch - Page ${pageNum} (start=${start}, limit=${PAGE_SIZE})`,
                body: {
                    "start": start,
                    "limit": PAGE_SIZE,
                    "fields": canonicalNames
                }
            };

            console.log(`\n📄 Fetching Page ${pageNum}/${MAX_PAGES}...`);
            const result = await fetchDataWithCanonicalFields(endpoint.url, token, endpoint.description, endpoint.body);
            results.push(result);
            
            if (result.count && typeof result.count === 'number') {
                totalCount += result.count;
                
                // Check if we got less than the page size - means no more data
                if (result.count < PAGE_SIZE) {
                    hasMoreData = false;
                    console.log(`✅ Reached end of data at page ${pageNum} (got ${result.count} records)`);
                }
            } else {
                // Error or no data - stop pagination
                hasMoreData = false;
                if (result.error) {
                    console.log(`❌ Error on page ${pageNum} - stopping pagination`);
                }
            }
            
            currentPage++;
            
            // Add delay between requests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        if (currentPage >= MAX_PAGES && hasMoreData) {
            console.log(`\n⚠️ Reached maximum pages (${MAX_PAGES}) - there might be more data available!`);
        }

        // Process and extract canonical data
        console.log('\n🔄 PROCESSING CANONICAL DATA');
        console.log('='.repeat(70));

        const allCanonicalData = [];
        let processedCount = 0;

        for (let pageIndex = 0; pageIndex < results.length; pageIndex++) {
            const result = results[pageIndex];
            const pageNumber = pageIndex + 1;

            console.log(`\n🔍 PROCESSING PAGE ${pageNumber}: ${result.description}`);
            console.log('-'.repeat(50));

            if (result.data && Array.isArray(result.data)) {
                console.log(`📊 Processing ${result.data.length} contacts with canonical fields...`);

                result.data.forEach((contactData, i) => {
                    try {
                        const canonicalData = extractCanonicalData(contactData, fieldDefinitions);

                        // Add page information
                        canonicalData.metadata.pageNumber = pageNumber;
                        canonicalData.metadata.positionInPage = i + 1;

                        allCanonicalData.push(canonicalData);
                        processedCount++;

                        // Log first few processed records from first page
                        if (pageIndex === 0 && i < 3) {
                            console.log(`\n📋 Sample Contact ${i + 1}:`);
                            console.log(`   🆔 ID: ${canonicalData.contactId}`);
                            console.log(`   📊 Fields with data: ${canonicalData.metadata.fieldsWithData}/${canonicalData.metadata.fieldsExtracted}`);
                            console.log(`   👤 Basic Info fields: ${Object.keys(canonicalData.basicInfo).length}`);
                            console.log(`   📞 Contact Details fields: ${Object.keys(canonicalData.contactDetails).length}`);
                            console.log(`   🏠 Address fields: ${Object.keys(canonicalData.addresses).length}`);
                            console.log(`   💼 Employment fields: ${Object.keys(canonicalData.employment).length}`);
                            console.log(`   🎯 Opportunity fields: ${Object.keys(canonicalData.opportunity).length}`);
                        }

                    } catch (error) {
                        console.error(`   ❌ Error processing contact ${i + 1}:`, error.message);
                    }
                });

                console.log(`   ✅ Page ${pageNumber} Complete: Processed ${result.data.length} contacts`);
            } else {
                console.log(`   ❌ Page ${pageNumber}: No contact data to process`);
            }
        }

        console.log(`\n🎉 PROCESSING COMPLETE: ${processedCount} contacts processed with canonical fields`);

        // Summary
        console.log('\n📈 FETCH SUMMARY');
        console.log('='.repeat(70));
        console.log(`🔢 Total pages fetched: ${results.length}`);
        console.log(`📊 Total records across all pages: ${totalCount}`);
        console.log(`📋 Canonical fields requested: ${canonicalNames.length}`);
        console.log(`🎯 Dynamic pagination: ${hasMoreData ? 'Stopped due to page limit' : 'Reached end of data'}`);

        results.forEach((result, index) => {
            console.log(`\n📋 Page ${index + 1}: ${result.description}`);
            console.log(`   📊 Count: ${result.count}`);
            console.log(`   ⏰ Fetched at: ${result.fetchedAt}`);
            if (result.error) {
                console.log(`   ❌ Error: ${result.error}`);
            }
        });

        // Data quality analysis
        if (allCanonicalData.length > 0) {
            console.log('\n📈 DATA QUALITY ANALYSIS');
            console.log('='.repeat(70));

            const totalFields = allCanonicalData.reduce((sum, contact) => sum + contact.metadata.fieldsExtracted, 0);
            const fieldsWithData = allCanonicalData.reduce((sum, contact) => sum + contact.metadata.fieldsWithData, 0);
            const dataCompleteness = ((fieldsWithData / totalFields) * 100).toFixed(1);

            console.log(`📊 Overall data completeness: ${dataCompleteness}% (${fieldsWithData}/${totalFields} fields)`);

            // Category analysis
            const categoryStats = {
                basicInfo: 0,
                contactDetails: 0,
                addresses: 0,
                employment: 0,
                opportunity: 0,
                recentEvents: 0,
                appointments: 0,
                customFields: 0
            };

            allCanonicalData.forEach(contact => {
                Object.keys(categoryStats).forEach(category => {
                    categoryStats[category] += Object.keys(contact[category]).length;
                });
            });

            console.log('\n📋 Data by category:');
            Object.entries(categoryStats).forEach(([category, count]) => {
                const avgPerContact = (count / allCanonicalData.length).toFixed(1);
                console.log(`   ${category}: ${count} total fields (avg ${avgPerContact} per contact)`);
            });
        }

        // Save all data to JSON files
        console.log('\n💾 SAVING CANONICAL DATA TO JSON FILES');
        console.log('='.repeat(70));

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

        try {
            // Save raw canonical data from all pages
            const rawDataFile = `encompass-canonical-raw-${timestamp}.json`;
            const rawDataToSave = {
                metadata: {
                    fetchedAt: new Date().toISOString(),
                    totalPages: results.length,
                    totalRecords: totalCount,
                    canonicalFieldsRequested: canonicalNames.length,
                    paginationComplete: !hasMoreData,
                    apiServer: API_SERVER
                },
                canonicalFields: canonicalNames,
                fieldDefinitions: fieldDefinitions,
                pages: results.map((result, index) => ({
                    pageNumber: index + 1,
                    description: result.description,
                    count: result.count,
                    fetchedAt: result.fetchedAt,
                    error: result.error || null,
                    data: result.data || []
                }))
            };

            fs.writeFileSync(rawDataFile, JSON.stringify(rawDataToSave, null, 2));
            console.log(`✅ Raw canonical data saved to: ${rawDataFile}`);
            console.log(`   📊 File size: ${(fs.statSync(rawDataFile).size / 1024 / 1024).toFixed(2)} MB`);

            // Save processed canonical data
            if (allCanonicalData.length > 0) {
                const processedDataFile = `encompass-canonical-processed-${timestamp}.json`;
                const processedDataToSave = {
                    metadata: {
                        processedAt: new Date().toISOString(),
                        totalRecords: allCanonicalData.length,
                        canonicalFieldsUsed: canonicalNames.length,
                        source: 'Encompass API - Canonical Fields',
                        version: '1.0'
                    },
                    canonicalData: allCanonicalData
                };

                fs.writeFileSync(processedDataFile, JSON.stringify(processedDataToSave, null, 2));
                console.log(`✅ Processed canonical data saved to: ${processedDataFile}`);
                console.log(`   📊 File size: ${(fs.statSync(processedDataFile).size / 1024 / 1024).toFixed(2)} MB`);
            }

            // Save field mapping and statistics
            const mappingFile = `encompass-canonical-mapping-${timestamp}.json`;
            const mappingData = {
                fieldMapping: {
                    timestamp: new Date().toISOString(),
                    totalFieldsDefined: fieldDefinitions.length,
                    selectableFields: canonicalNames.length,
                    fieldsByCategory: {}
                },
                fieldDefinitions: fieldDefinitions.map(field => ({
                    canonicalName: field.canonicalName,
                    description: field.description,
                    category: field.category,
                    dataType: field.dataType,
                    filterOnly: field.filterOnly,
                    options: field.options || null
                }))
            };

            // Group fields by category
            fieldDefinitions.forEach(field => {
                if (!mappingData.fieldMapping.fieldsByCategory[field.category]) {
                    mappingData.fieldMapping.fieldsByCategory[field.category] = 0;
                }
                mappingData.fieldMapping.fieldsByCategory[field.category]++;
            });

            fs.writeFileSync(mappingFile, JSON.stringify(mappingData, null, 2));
            console.log(`✅ Field mapping saved to: ${mappingFile}`);

        } catch (error) {
            console.error('❌ Error saving JSON files:', error.message);
        }

        console.log('\n✅ Canonical fields fetch script completed successfully!');

    } catch (error) {
        console.error('\n❌ Script failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();
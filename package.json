{"name": "encompass-ghl-sync", "version": "1.0.0", "description": "Comprehensive Encompass to GoHighLevel sync system with incremental updates and cron scheduling", "main": "test-comprehensive-encompass-data.js", "scripts": {"start": "node test-comprehensive-encompass-data.js", "sync": "node test-comprehensive-encompass-data.js 0 sync", "cron": "node test-comprehensive-encompass-data.js 0 cron", "fetch": "node test-comprehensive-encompass-data.js 1000 contacts", "comprehensive": "node test-comprehensive-encompass-data.js 25 comprehensive", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["encompass", "gohighlevel", "sync", "mortgage", "crm", "automation", "incremental"], "author": "Encompass GHL Sync System", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.6.1", "node-cron": "^3.0.3"}}
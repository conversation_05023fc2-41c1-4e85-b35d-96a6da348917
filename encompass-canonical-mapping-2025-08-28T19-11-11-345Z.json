{"fieldMapping": {"timestamp": "2025-08-28T19:11:11.474Z", "totalFieldsDefined": 123, "selectableFields": 123, "fieldsByCategory": {"Borrower Contact": 43, "Opportunity": 22, "Recent Events": 45, "Appointments": 10, "BorrowerCustomField": 3}}, "fieldDefinitions": [{"canonicalName": "Contact.FirstName", "description": "Borrower First Name", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.LastName", "description": "Borrower Last Name", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.MiddleName", "description": "Borrower Middle Name", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.SuffixName", "description": "Borrower Suffix", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.PrimContact", "description": "Primary Contact", "category": "Borrower Contact", "dataType": "YN", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomePhone", "description": "Home Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.WorkPhone", "description": "Work Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.MobilePhone", "description": "Cell Phone", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.FaxNumber", "description": "Fax Number", "category": "Borrower Contact", "dataType": "PHONE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.PersonalEmail", "description": "Home Email", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizEmail", "description": "Work Email", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.DoNotCall", "description": "Do not call", "category": "Borrower Contact", "dataType": "YN", "filterOnly": false, "options": null}, {"canonicalName": "Contact.DoNotSpam", "description": "Do not email", "category": "Borrower Contact", "dataType": "YN", "filterOnly": false, "options": null}, {"canonicalName": "Contact.DoNotFax", "description": "Do not fax", "category": "Borrower Contact", "dataType": "YN", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomeAddress1", "description": "Home Address1", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomeAddress2", "description": "Home Address2", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomeCity", "description": "Home Address City", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomeState", "description": "Home Address State", "category": "Borrower Contact", "dataType": "STATE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.HomeZip", "description": "Home Address Zip", "category": "Borrower Contact", "dataType": "ZIPCODE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Status", "description": "Status", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": ["Inactive", "Cold Lead", "Warm Lead", "Hot Lead", "Live Lead", "Active Loan(s)"]}, {"canonicalName": "Contact.EmployerName", "description": "Business Company", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.JobTitle", "description": "Business Title", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizAddress1", "description": "Business Address1", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizAddress2", "description": "Business Address2", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizCity", "description": "Business City", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizState", "description": "Business State", "category": "Borrower Contact", "dataType": "STATE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizZip", "description": "Business Zip", "category": "Borrower Contact", "dataType": "ZIPCODE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.BizWebUrl", "description": "Business Web URL", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Birthdate", "description": "Birthday", "category": "Borrower Contact", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Contact.SSN", "description": "SSN", "category": "Borrower Contact", "dataType": "SSN", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Referral", "description": "Referral", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Income", "description": "Annual Income", "category": "Borrower Contact", "dataType": "INTEGER", "filterOnly": false, "options": null}, {"canonicalName": "Contact.MaritalStatus", "description": "Married", "category": "Borrower Contact", "dataType": "YN", "filterOnly": false, "options": null}, {"canonicalName": "Contact.SpouseName", "description": "Spouse", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Anniversary", "description": "Anniversary", "category": "Borrower Contact", "dataType": "MONTHDAY", "filterOnly": false, "options": null}, {"canonicalName": "ContactOwner.UserName", "description": "Contact Owner", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.ContactType", "description": "Contact Type", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": ["Prospect", "Client", "Lead"]}, {"canonicalName": "Contact.AccessLevel", "description": "Access Level", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": ["Public", "Private"]}, {"canonicalName": "ContactGroupCount.GroupCount", "description": "Groups", "category": "Borrower Contact", "dataType": "INTEGER", "filterOnly": false, "options": null}, {"canonicalName": "ContactLoanCount.LoanCount", "description": "Associated Loans", "category": "Borrower Contact", "dataType": "INTEGER", "filterOnly": false, "options": null}, {"canonicalName": "ContactCompleteLoanCount.LoanCount", "description": "Associated Complete Loans", "category": "Borrower Contact", "dataType": "INTEGER", "filterOnly": false, "options": null}, {"canonicalName": "Contact.Salutation", "description": "Salutation", "category": "Borrower Contact", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Contact.LastModified", "description": "Last Modification", "category": "Borrower Contact", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.LoanAmount", "description": "Opportunity Loan Amount", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.Purpose", "description": "<PERSON><PERSON>", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": ["Purchase", "Cash-Out Refi", "No Cash-Out Refi", "Construction", "Construction - Perm", "Other"]}, {"canonicalName": "Opportunity.PurposeOther", "description": "Other Purpose", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.Term", "description": "<PERSON>an <PERSON>", "category": "Opportunity", "dataType": "INTEGER", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.Amortization", "description": "Amortization", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": ["Fixed Rate", "GPM", "ARM", "Other"]}, {"canonicalName": "Opportunity.DownPayment", "description": "Available Down Payment", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.MortgageBalance", "description": "Current Mortgage Balance", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.MortgageRate", "description": "Current Mortgage Rate %", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.HousingPayment", "description": "Monthly Payment (housing)", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.NonHousingPayment", "description": "Monthly Payment (non-housing)", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.PropertyAddress", "description": "Property Address", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.PropertyCity", "description": "Property City", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.PropertyState", "description": "Property State", "category": "Opportunity", "dataType": "STATE", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.PropertyZip", "description": "Property Zip", "category": "Opportunity", "dataType": "ZIPCODE", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.PropertyUse", "description": "Property Use", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": ["Primary", "Secondary", "Investment"]}, {"canonicalName": "Opportunity.PropertyType", "description": "Property Type", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": ["Attached", "Condominium", "Co-Operative", "Detached", "High Rise Condominium", "Manufactured Housing", "PUD"]}, {"canonicalName": "Opportunity.PropertyValue", "description": "Property Value", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.CashOut", "description": "Cash Out", "category": "Opportunity", "dataType": "DECIMAL_4", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.PurchaseDate", "description": "Property Purchase date", "category": "Opportunity", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.CreditRating", "description": "Credit Rating", "category": "Opportunity", "dataType": "INTEGER", "filterOnly": false, "options": null}, {"canonicalName": "Opportunity.Employment", "description": "Employment", "category": "Opportunity", "dataType": "STRING", "filterOnly": false, "options": ["Employed", "Self-Employed", "Unemployed"]}, {"canonicalName": "Opportunity.Bankruptcy", "description": "Bankruptcy", "category": "Opportunity", "dataType": "YN", "filterOnly": false, "options": null}, {"canonicalName": "LastMailMerge.LetterName", "description": "Last Mail Merge Doc", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastMailMerge.TimeOfHistory", "description": "Last Mail Merge Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastMailMerge.Sender", "description": "Last Mail Merge User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastEmailMerge.LetterName", "description": "Last Email Merge Doc", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastEmailMerge.TimeOfHistory", "description": "Last Email Merge Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastEmailMerge.Sender", "description": "Last Email Merge User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastEmailMerge.Subject", "description": "Last Email Merge Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCall.TimeOfHistory", "description": "Last Call Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCall.Sender", "description": "Last Call User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCallNotes.Subject", "description": "Last Call Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCallNotes.Details", "description": "Last Call Details", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastEmail.TimeOfHistory", "description": "Last Email Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastEmail.Sender", "description": "Last Email User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastEmailNotes.Subject", "description": "Last Email Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastEmailNotes.Details", "description": "Last Email Details", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastFax.TimeOfHistory", "description": "Last Fax Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastFax.Sender", "description": "Last Fax User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastFaxNotes.Subject", "description": "Last Fax Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastFaxNotes.Details", "description": "Last Fax Details", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignEmail.TimeOfHistory", "description": "Last Campaign Email Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignEmail.Sender", "description": "Last Campaign Email User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignEmail.Subject", "description": "Last Campaign Email Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignEmail.LetterName", "description": "Last Campaign Email <PERSON>ment", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignEmail.CampaignActivityStatus", "description": "Last Campaign Email Status", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignEmail.CampaignScheduledDate", "description": "Last Campaign Email Schedule", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignFax.TimeOfHistory", "description": "Last Campaign Fax Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignFax.Sender", "description": "Last Campaign Fax User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignFax.Subject", "description": "Last Campaign Fax Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignFax.CampaignActivityStatus", "description": "Last Campaign Fax Status", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignFax.CampaignScheduledDate", "description": "Last Campaign Fax Schedule", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignCall.TimeOfHistory", "description": "Last Campaign Call Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignCall.Sender", "description": "Last Campaign Call User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignCall.Subject", "description": "Last Campaign Call Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignCall.CampaignActivityStatus", "description": "Last Campaign Call Status", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignCall.CampaignScheduledDate", "description": "Last Campaign Call Schedule", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignLetter.TimeOfHistory", "description": "Last Campaign Letter Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignLetter.Sender", "description": "Last Campaign Letter User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignLetter.LetterName", "description": "Last Campaign Letter Document", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignLetter.CampaignActivityStatus", "description": "Last Campaign Letter Status", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignLetter.CampaignScheduledDate", "description": "Last Campaign Letter Schedule", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignReminder.TimeOfHistory", "description": "Last Campaign Reminder Date", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignReminder.Sender", "description": "Last Campaign Reminder User ID", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignReminder.Subject", "description": "Last Campaign Reminder Subject", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignReminder.CampaignActivityStatus", "description": "Last Campaign Reminder Status", "category": "Recent Events", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastCampaignReminder.CampaignScheduledDate", "description": "Last Campaign Reminder Schedule", "category": "Recent Events", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "NextAppointment.StartDateTime", "description": "Next Appointment Date", "category": "Appointments", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "NextAppointment.Subject", "description": "Next Appointment Subject", "category": "Appointments", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "NextAppointment.StartDateTime", "description": "Next Appointment Start Time", "category": "Appointments", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "NextAppointment.EndDateTime", "description": "Next Appointment End Time", "category": "Appointments", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "NextAppointment.Description", "description": "Next Appointment Comments", "category": "Appointments", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastAppointment.StartDateTime", "description": "Most Recent Appointment Date", "category": "Appointments", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastAppointment.Subject", "description": "Most Recent Appointment Subject", "category": "Appointments", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "LastAppointment.StartDateTime", "description": "Most Recent Appointment Start Time", "category": "Appointments", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastAppointment.EndDateTime", "description": "Most Recent Appointment End Time", "category": "Appointments", "dataType": "DATE", "filterOnly": false, "options": null}, {"canonicalName": "LastAppointment.Description", "description": "Most Recent Appointment Comments", "category": "Appointments", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "CUSTOM.LEAD SOURCE", "description": "Lead Source", "category": "BorrowerCustomField", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "CUSTOM.PROMO CODE", "description": "Promo Code", "category": "BorrowerCustomField", "dataType": "STRING", "filterOnly": false, "options": null}, {"canonicalName": "CUSTOM.LEAD ORIGIN", "description": "Lead Origin", "category": "BorrowerCustomField", "dataType": "DROPDOWNLIST", "filterOnly": false, "options": ["Accountant", "Attorney", "Bank", "Builder", "<PERSON><PERSON><PERSON>", "Financial Advisor", "Leads360", "Networking Group", "Realtor", "Referral - Family", "Telemarketing", "Tele_01", "Union/Organization", "WebCenter", "Promo Code"]}]}
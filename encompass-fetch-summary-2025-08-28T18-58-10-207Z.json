{"fetchSummary": {"timestamp": "2025-08-28T18:58:10.235Z", "totalPages": 10, "totalRecords": 10000, "paginationComplete": false, "successfulPages": 10, "failedPages": 0}, "pageBreakdown": [{"page": 1, "records": 1000, "success": true, "error": null}, {"page": 2, "records": 1000, "success": true, "error": null}, {"page": 3, "records": 1000, "success": true, "error": null}, {"page": 4, "records": 1000, "success": true, "error": null}, {"page": 5, "records": 1000, "success": true, "error": null}, {"page": 6, "records": 1000, "success": true, "error": null}, {"page": 7, "records": 1000, "success": true, "error": null}, {"page": 8, "records": 1000, "success": true, "error": null}, {"page": 9, "records": 1000, "success": true, "error": null}, {"page": 10, "records": 1000, "success": true, "error": null}], "dataQuality": {"totalRecords": 100, "withEmails": 100, "withPhones": 100, "withAddresses": 100, "withEmployers": 0, "withReferrals": 100}}